{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "pages:build": "npx @cloudflare/next-on-pages", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy"}, "dependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@google/generative-ai": "^0.17.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.4.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-table": "^8.21.3", "@types/archiver": "^6.0.3", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.17", "@types/papaparse": "^5.3.16", "archiver": "^7.0.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "eslint-plugin-next-on-pages": "^1.13.12", "file-saver": "^2.0.5", "http-proxy-middleware": "^3.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.417.0", "next": "^14.2.29", "next-themes": "^0.3.0", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react18-json-view": "^0.2.9", "recharts": "^2.15.3", "sonner": "^1.7.4", "supabase": "^1.226.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.56"}, "devDependencies": {"@types/node": "^20.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^8.57.1", "eslint-config-next": "14.2.5", "postcss": "^8.5.4", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}