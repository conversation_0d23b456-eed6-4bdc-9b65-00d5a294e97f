'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  exportSignatureScraperResults,
  exportSuccessfulResults,
  type CreatorSignatureInfo,
  type ExportOptions,
} from '@/lib/export/signature-scraper';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  results: CreatorSignatureInfo[];
  failedCreators: string[];
}

const AVAILABLE_COLUMNS = [
  { id: 'Status', label: 'Status', default: true },
  { id: 'Unique ID', label: 'TikTok Username', default: true },
  { id: 'Signature', label: 'Bio/Signature', default: true },
  { id: 'Bio URL', label: 'Bio/Link URL', default: true },
  { id: 'Youtube Channel ID', label: 'YouTube', default: true },
  { id: 'Instagram ID', label: 'Instagram', default: true },
  { id: 'Twitter ID', label: 'Twitter', default: true },
  { id: 'Email', label: 'Email', default: true },
];

export function ExportDialog({
  open,
  onOpenChange,
  results,
  failedCreators,
}: ExportDialogProps) {
  const [exportType, setExportType] = useState<'all' | 'success'>('all');
  const [includeTimestamp, setIncludeTimestamp] = useState(true);
  const [selectedColumns, setSelectedColumns] = useState<string[]>(
    AVAILABLE_COLUMNS.filter((col) => col.default).map((col) => col.id)
  );
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const handleColumnToggle = (columnId: string) => {
    setSelectedColumns((prev) =>
      prev.includes(columnId)
        ? prev.filter((id) => id !== columnId)
        : [...prev, columnId]
    );
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      const options: ExportOptions = {
        includeTimestamp,
        columns: selectedColumns,
        onProgress: setExportProgress,
      };

      if (exportType === 'success') {
        await exportSuccessfulResults(results, options);
      } else {
        await exportSignatureScraperResults(results, failedCreators, options);
      }

      // Close dialog after successful export
      setTimeout(() => {
        onOpenChange(false);
        setExportProgress(0);
      }, 500);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const totalRecords =
    exportType === 'all'
      ? results.length + failedCreators.length
      : results.length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Export Results</DialogTitle>
          <DialogDescription>
            Configure your export settings and select which data to include.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Export Type */}
          <div className="space-y-3">
            <Label htmlFor="export-type">Export Type</Label>
            <Select
              value={exportType}
              onValueChange={(value: 'all' | 'success') => setExportType(value)}
            >
              <SelectTrigger id="export-type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  All results ({results.length + failedCreators.length} records)
                </SelectItem>
                <SelectItem value="success">
                  Successful only ({results.length} records)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Columns Selection */}
          <div className="space-y-3">
            <Label>Select Columns</Label>
            <div className="space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
              {AVAILABLE_COLUMNS.map((column) => (
                <div key={column.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={column.id}
                    checked={selectedColumns.includes(column.id)}
                    onCheckedChange={() => handleColumnToggle(column.id)}
                  />
                  <Label
                    htmlFor={column.id}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {column.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center justify-between">
            <Label htmlFor="timestamp" className="cursor-pointer">
              Include timestamp in filename
            </Label>
            <Switch
              id="timestamp"
              checked={includeTimestamp}
              onCheckedChange={setIncludeTimestamp}
            />
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-2">
              <Progress value={exportProgress} />
              <p className="text-sm text-muted-foreground text-center">
                Preparing export... {exportProgress}%
              </p>
            </div>
          )}

          {/* Summary */}
          <div className="bg-muted p-3 rounded-md">
            <p className="text-sm">
              <span className="font-medium">Export Summary:</span>
              <br />• {totalRecords} total records
              <br />• {selectedColumns.length} columns selected
              <br />• Format: Excel (.xlsx)
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isExporting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || selectedColumns.length === 0}
          >
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
