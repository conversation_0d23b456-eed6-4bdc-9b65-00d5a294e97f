import React from "react";
import Link from "next/link";
import { Home, Menu, Search, Squirrel, Users, Video } from "lucide-react";
import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ModeToggle } from "./ModeToggle";
import { createServerClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";

const sidebarItems = [
  { name: "Overview", href: "/dashboard", icon: Home },
  {
    name: "KOL Scout",
    icon: Search,
    subItems: [
      { name: "All Scouts", href: "/dashboard/scout" },
      { name: "New Scout", href: "/dashboard/scout/create" },
      { name: "Scouted Videos", href: "/dashboard/scout/videos" },
      { name: "Scouted Creators", href: "/dashboard/scout/creators" },
    ],
  },
  {
    name: "Utils",
    icon: Video,
    subItems: [
      { name: "Localize Videos", href: "/dashboard/utils/localize-videos" },
      { name: "TikTok Downloader", href: "/dashboard/utils/tiktok-downloader" },
      {
        name: "Signatures Scraper",
        href: "/dashboard/utils/signature-scraper",
      },
      {
        name: "Audit Video",
        href: "/dashboard/utils/audit-video",
      },
    ],
  },
];

interface SidebarProps {
  isVisible: boolean;
}

export default async function Sidebar({ isVisible }: SidebarProps) {
  const cookieStore = cookies();
  const supabase = createServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  const pathname = cookieStore.get("pathname")?.value || "/";

  const handleLogout = async () => {
    "use server";
    const cookieStore = cookies();
    const supabase = createServerClient();
    await supabase.auth.signOut();
    cookieStore.delete("supabase-auth-token");
    redirect("/login");
  };

  const SidebarContent = () => (
    <>
      <div className="flex h-14 items-center border-b px-4 justify-between">
        <Link href="/" className="flex items-center gap-2 font-semibold">
          <Squirrel className="h-6 w-6" />
          <span className="">SSS</span>
        </Link>
        <ModeToggle />
      </div>
      <ScrollArea className="flex-1 px-3 py-2 overflow-y-auto">
        <nav className="grid items-start gap-2">
          {sidebarItems.map((item, index) =>
            item.subItems
              ? (
                <Accordion
                  key={index}
                  type="single"
                  collapsible
                  className="w-full"
                  defaultValue={item.subItems.some((subItem) =>
                      pathname === subItem.href
                    )
                    ? item.name
                    : undefined}
                >
                  <AccordionItem value={item.name} className="border-none">
                    <AccordionTrigger className="py-2 px-3 justify-start hover:no-underline hover:bg-muted text-muted-foreground">
                      <span className="flex items-center gap-3 flex-grow">
                        {item.icon && <item.icon className="h-4 w-4" />}
                        {item.name}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="pt-1 pb-2 bg-muted/50">
                      <div className="grid gap-1 pl-6">
                        {item.subItems.map((subItem, subIndex) => (
                          <Link
                            key={subIndex}
                            href={subItem.href}
                            className={cn(
                              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:text-primary",
                              pathname === subItem.href
                                ? "bg-muted text-primary"
                                : "text-muted-foreground",
                            )}
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              )
              : (
                <Link
                  key={index}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:text-primary",
                    pathname === item.href
                      ? "bg-muted text-primary"
                      : "text-muted-foreground",
                  )}
                >
                  {item.icon && <item.icon className="h-4 w-4" />}
                  {item.name}
                </Link>
              )
          )}
        </nav>
      </ScrollArea>
    </>
  );

  const UserSection = () => (
    <div className="mt-auto p-4 border-t">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="w-full justify-start gap-2">
            <Avatar className="h-8 w-8 mr-2">
              <AvatarImage
                src={`https://api.dicebear.com/9.x/thumbs/svg?seed=${
                  user?.id || "default"
                }`}
              >
              </AvatarImage>
            </Avatar>
            <span>{user?.email?.split("@")[0] || "User"}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">My account</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user?.email || "No email provided"}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href="/update-password">Update Password</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <form action={handleLogout}>
              <button type="submit" className="w-full text-left">
                Log out
              </button>
            </form>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  const SidebarWithUserSection = () => (
    <>
      <SidebarContent />
      <UserSection />
    </>
  );

  if (isVisible) {
    return (
      <div className="border-r bg-muted/40 w-52 h-screen flex flex-col">
        <SidebarWithUserSection />
      </div>
    );
  } else {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="shrink-0 md:hidden mr-2"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-72">
          <nav className="flex flex-col h-full">
            <SidebarWithUserSection />
          </nav>
        </SheetContent>
      </Sheet>
    );
  }
}
