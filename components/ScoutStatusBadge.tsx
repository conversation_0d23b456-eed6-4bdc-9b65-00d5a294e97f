import React from 'react';
import { Badge } from '@/components/ui/badge';

const ScoutStatusBadge = ({ status }: { status: string }) => {
  // Function to extract the status
  const extractStatus = (fullStatus: string) => {
    const match = fullStatus.match(/\[.*?]\s*(.*)/);
    return match ? match[1] : fullStatus;
  };

  // Extract the status
  const displayStatus = extractStatus(status);

  // Determine if the status is 'completed'
  const isCompleted = displayStatus.toLowerCase() === 'completed';

  const isError =
    displayStatus.toLowerCase().includes('failed') ||
    displayStatus.toLowerCase().includes('error');

  return (
    <Badge
      variant={isError ? 'destructive' : isCompleted ? 'outline' : 'secondary'}
    >
      {displayStatus}
    </Badge>
  );
};

export default ScoutStatusBadge;
