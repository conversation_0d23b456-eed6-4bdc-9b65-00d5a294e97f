import React from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Filter } from "lucide-react"
import { Select } from '@radix-ui/react-select'
import {
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger

} from '@/components/ui/select'
import { Label } from 'recharts'

interface FilterInputProps {
    id: string;
    label: string;
    placeholder: string;
    value: string;
    onChange: (value: string) => void;
    type?: string;
    options?: Array<{ value?: string; label: string }>;
}

const FilterInput: React.FC<FilterInputProps> = ({ id, label, placeholder, value, onChange, type = "text", options }) => (
    <div className="flex flex-col space-y-2">
        <label htmlFor={id} className="text-sm font-medium">{label}</label>
        {
            type === "select" ?
                <Select onValueChange={(value) => onChange(value)}>
                    <SelectTrigger>
                        {value || placeholder}
                    </SelectTrigger>
                    <SelectContent>
                        <SelectGroup>
                            {
                                options?.map((option) => (option.value &&
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))
                            }
                        </SelectGroup>
                    </SelectContent>
                </Select>
                : <Input
                    id={id}
                    type={type}
                    placeholder={placeholder}
                    value={value}
                    onChange={(event) => onChange(event.target.value)}
                />
        }

    </div>
)

interface FilterOptionsProps {
    filters: Record<string, string>;
    setFilters: React.Dispatch<React.SetStateAction<Record<string, string>>>;
    applyFilters: (newFilters: Record<string, string>) => void;
    filterFields: Array<{
        id: string;
        label: string;
        placeholder: string;
        type?: string;
        options?: Array<{ value?: string; label: string }>;
    }>;
}

const FilterOptions: React.FC<FilterOptionsProps> = ({ filters, setFilters, applyFilters, filterFields }) => {
    const [open, setOpen] = React.useState(false)
    const [tempFilters, setTempFilters] = React.useState(filters)

    const handleApplyFilters = () => {
        setFilters(tempFilters);
        try {
            localStorage.setItem('creatorFilters:/dashboard/scout/creators', JSON.stringify(tempFilters));
        } catch (error) {
            console.error('Error saving filters to localStorage:', error);
        }
        applyFilters(tempFilters);
        setOpen(false);
    };

    React.useEffect(() => {
        setTempFilters(filters);
    }, [filters]);

    return (
        <Dialog
            open={open}
            onOpenChange={(newOpen) => {
                if (newOpen) {
                    setTempFilters({ ...filters });
                }
                setOpen(newOpen);
            }}
        >
            <DialogTrigger asChild>
                <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Filter Options</DialogTitle>
                    <DialogDescription>Set your filter criteria here.</DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4 py-4">
                    {filterFields.map((field) => (
                        <FilterInput
                            key={field.id}
                            id={`${field.id}-filter`}
                            label={field.label}
                            placeholder={field.placeholder}
                            type={field.type}
                            value={tempFilters[field.id]}
                            options={field.options}
                            onChange={(value) => setTempFilters(prev => ({ ...prev, [field.id]: value }))}
                        />
                    ))}
                </div>
                <div className="flex justify-end">
                    <Button onClick={handleApplyFilters}>
                        Apply Filters
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default FilterOptions
