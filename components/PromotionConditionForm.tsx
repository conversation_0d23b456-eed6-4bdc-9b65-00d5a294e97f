"use client";

import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Loader2Icon } from "lucide-react";
import { useState } from "react";

export const auditConditonFormSchema = z.object({
    videoFile: z.instanceof(File),
    targetProduct: z.string().min(2).max(20),
    promotionalDuration: z.coerce.number().optional(),
    positionPercentage: z.number().optional(),
    videoTitle: z.string().optional(),
    videoHashtags: z.string().optional(),
});

interface PromotionConditionsFormProps {
    onFormSubmit: (values: z.infer<typeof auditConditonFormSchema>) => void;
    disabled?: boolean;
}

export default function PromotionConditionsForm(
    { onFormSubmit, disabled }: PromotionConditionsFormProps,
) {
    const [requirePosition, setRequirePosition] = useState(0);
    const form = useForm<z.infer<typeof auditConditonFormSchema>>({
        resolver: zodResolver(auditConditonFormSchema),
    });

    function onSubmit(values: z.infer<typeof auditConditonFormSchema>) {
        try {
            onFormSubmit(values);
        } catch (error) {
            console.error("Form submission error", error);
            toast.error("Failed to submit the form. Please try again.");
        }
    }

    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="max-w-3xl"
            >
                <div className="grid grid-cols-2 gap-4">
                    <FormField
                        control={form.control}
                        name="videoFile"
                        render={({ field: { value, onChange, ...field } }) => (
                            <FormItem>
                                <FormLabel>Video File</FormLabel>
                                <FormControl>
                                    <Input
                                        onChange={(event) =>
                                            onChange(
                                                event.target.files &&
                                                    event.target.files[0],
                                            )}
                                        type="file"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="targetProduct"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Target Product</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Input the product name"
                                        type="text"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="videoTitle"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Video Title</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Video Title"
                                        type="string"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="videoHashtags"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Video Hashtags</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Video Hashtags"
                                        type="string"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="promotionalDuration"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Required Promotional Segment (seconds)
                                </FormLabel>
                                <FormControl>
                                    <Input
                                        defaultValue={0}
                                        placeholder="Valid promotion segment seconds"
                                        type="number"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="positionPercentage"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    Required Promotion Position
                                    ({requirePosition}%)
                                </FormLabel>
                                <FormControl>
                                    <div>
                                        <Slider
                                            defaultValue={[0]}
                                            min={0}
                                            max={100}
                                            step={1}
                                            aria-label="Valid promotion segments appear position"
                                            onValueChange={(
                                                value: number[],
                                            ) => {
                                                const inputValue = value[0];
                                                setRequirePosition(inputValue);
                                                field.onChange(inputValue);
                                            }}
                                        />
                                        <span
                                            className="mt-4 flex w-full items-center justify-between gap-1 text-xs font-medium text-muted-foreground"
                                            aria-hidden="true"
                                        >
                                            <span>0 %</span>
                                            <span>100 %</span>
                                        </span>
                                    </div>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
                <div className="mt-8">
                    <Button type="submit" disabled={disabled}>
                        {disabled
                            ? (
                                <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                            )
                            : null}
                        {disabled ? "Analyzing..." : "Submit"}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
