'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import J<PERSON><PERSON><PERSON> from 'jszip';
import axios, { AxiosResponse } from 'axios';

const MAX_RETRIES = 3;
const INITIAL_RETRY_DELAY = 1000; // 1 second

async function fetchWithRetry(
  url: string,
  retries = MAX_RETRIES,
  delay = INITIAL_RETRY_DELAY
): Promise<AxiosResponse> {
  try {
    const response = await axios.get(`/api/proxy-download?url=${encodeURIComponent(url)}`, {
      responseType: 'arraybuffer',
    });
    return response;
  } catch (error) {
    if (retries > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
      return fetchWithRetry(url, retries - 1, delay * 2);
    } else {
      throw error;
    }
  }
}

interface TikTokDownloaderFormProps {
  className?: string;
}

export default function TikTokDownloaderForm({
  className,
}: TikTokDownloaderFormProps) {
  const [tiktokLinks, setTiktokLinks] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    const links = tiktokLinks.split('\n').filter((link) => link.trim() !== '');

    if (links.length > 20) {
      toast.error('Too many links. Maximum allowed is 20.');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ links }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || 'Failed to download videos');
      }

      const data = await response.json();
      const { downloadInfo } = data;

      if (downloadInfo.length === 0) {
        toast.error(
          'No videos could be downloaded. Please check your links and try again.'
        );
      } else {
        const zip = new JSZip();

        // Download all videos and add them to the zip file
        const downloadPromises = downloadInfo.map(
          async ({ url, awemeId }: { url: string; awemeId: string }) => {
            try {
              const response = await fetchWithRetry(url);
              zip.file(`${awemeId}.mp4`, response.data);
            } catch (error) {
              console.error(
                `Failed to download video ${awemeId} after multiple retries:`,
                error
              );
              toast.error(
                `Failed to download video ${awemeId}. It will be skipped.`
              );
            }
          }
        );

        await Promise.all(downloadPromises);

        // Generate the zip file
        const zipBlob = await zip.generateAsync({ type: 'blob' });

        // Create a download link for the zip file
        const url = window.URL.createObjectURL(zipBlob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'tiktok_videos.zip';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success(
          `${downloadInfo.length} out of ${links.length} videos downloaded and zipped. Check your downloads folder.`
        );
      }
    } catch (error) {
      console.error('Error downloading videos:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to download videos. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="tiktok-links">TikTok Video Links (Max 20)</Label>
          <Textarea
            className="w-max-full"
            id="tiktok-links"
            placeholder="https://www.tiktok.com/@username/video/1234567890123456789
https://www.tiktok.com/@username/video/1234567890123456789"
            value={tiktokLinks}
            onChange={(e) => setTiktokLinks(e.target.value)}
            required
          />
          <div>
            <span className="text-sm text-gray-500 mt-4">
              Provide up to 20 links, one per line.
            </span>
          </div>
        </div>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Downloading...' : 'Download Videos'}
        </Button>
      </div>
    </form>
  );
}
