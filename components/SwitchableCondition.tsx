import React from 'react';
import { FormItem, FormLabel, FormDescription, FormControl } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { UseFormReturn } from "react-hook-form";
import { Option } from "@/components/MultipleSelector";

interface SwitchableConditionProps {
  form: UseFormReturn<any>;
  name: string;
  label: string;
  description: string;
  inputType: 'number' | 'text' | 'select';
  options?: Option[];
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: string | number;
}

const SwitchableCondition: React.FC<SwitchableConditionProps> = ({
  form,
  name,
  label,
  description,
  inputType,
  options,
  min,
  max,
  step,
  defaultValue,
}) => {
  const isEnabled = form.watch(name) !== null;

  const handleToggle = (checked: boolean) => {
    form.setValue(name, checked ? (defaultValue ?? '') : null);
  };

  const commonClassName = "w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50";

  const renderInput = () => {
    switch (inputType) {
      case 'number':
        return (
          <input
            type="number"
            {...form.register(name, { valueAsNumber: true })}
            min={min}
            max={max}
            step={step || 1}
            className={commonClassName}
          />
        );
      case 'select':
        return (
          <select
            {...form.register(name)}
            className={commonClassName}
          >
            {options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      default:
        return (
          <input
            type="text"
            {...form.register(name)}
            className={commonClassName}
          />
        );
    }
  };

  return (
    <FormItem className="space-y-2">
      <div className="flex justify-between items-center">
        <FormLabel>{label}</FormLabel>
        <Switch
          checked={isEnabled}
          onCheckedChange={handleToggle}
        />
      </div>
      <div className={`mt-2 transition-all duration-300 ${isEnabled ? 'opacity-100 max-h-20' : 'opacity-0 max-h-0 overflow-hidden'}`}>
        <FormControl>{renderInput()}</FormControl>
        {description && (
          <FormDescription className="text-xs text-muted-foreground mt-1">
            {description}
          </FormDescription>
        )}
      </div>
    </FormItem>
  );
};

export default SwitchableCondition;
