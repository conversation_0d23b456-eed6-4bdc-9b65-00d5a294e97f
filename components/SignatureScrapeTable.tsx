import { Table } from "lucide-react";
import {
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

interface SignatureScrapeTableProps {
    failedCreators: string[];
}

export default function SignatureScrapeTable(data: SignatureScrapeTableProps) {
    const { failedCreators } = data;
    return (
        <div>
            <h3 className="text-xl font-semibold mb-2">
                Failed Creators ({failedCreators
                    .length})
            </h3>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Status</TableHead>
                        <TableHead>Unique ID</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {failedCreators.map((id, index) => (
                        <TableRow key={index}>
                            <TableCell>
                                <span className="px-2 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800">
                                    Failed
                                </span>
                            </TableCell>
                            <TableCell>{id}</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
}
