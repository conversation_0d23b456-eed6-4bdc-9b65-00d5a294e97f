import React from 'react'
import {Checkbox} from "@/components/ui/checkbox"

const ReadOnlyCheckbox = ({checked = false, label = ""}) => {
    return (
        <div className="flex items-center space-x-2">
            <Checkbox
                checked={checked}
                onCheckedChange={() => {
                }}
                className="cursor-not-allowed"
            />
            {label && <label className="text-sm font-medium leading-none">{label}</label>}
        </div>
    )
}

export default ReadOnlyCheckbox