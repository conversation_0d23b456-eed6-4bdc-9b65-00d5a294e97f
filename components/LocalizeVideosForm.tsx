'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function LocalizeVideosForm({
  className,
}: {
  className?: string;
}) {
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState<string>('');
  const [subtitleUrl, setSubtitleUrl] = useState<string | null>(null);
  const [locale, setLocale] = useState<string>('cn-zh');
  const [modelType, setModelType] = useState<'speed' | 'quality'>('speed');
  const [originalSubtitle, setOriginalSubtitle] = useState<string>('');
  const [translatedSubtitle, setTranslatedSubtitle] = useState<string>('');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const uploadFileToGemini = async (
    file: File
  ): Promise<{ fileUri: string; fileName: string }> => {
    setStatus(`Uploading file: ${file.name}`);

    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/localize-video/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to upload file');
    }

    const result = await response.json();
    await ensureFileReady(result.fileUri);

    return result;
  };

  const ensureFileReady = async (fileUri: string): Promise<void> => {
    setStatus('Ensuring file is ready...');
    let state = 'PROCESSING';

    while (state === 'PROCESSING') {
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const response = await fetch(
        `/api/localize-video/status?fileUri=${encodeURIComponent(fileUri)}`
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to check file status');
      }

      const result = await response.json();
      state = result.state;
      setStatus(`File state: ${state}`);
    }

    if (state !== 'ACTIVE') {
      throw new Error(`File processing failed. Final state: ${state}`);
    }
  };

  const deleteFile = async (fileName: string): Promise<void> => {
    try {
      await fetch(
        `/api/localize-video/delete?fileName=${encodeURIComponent(fileName)}`,
        {
          method: 'DELETE',
        }
      );
    } catch (error) {
      console.error('Failed to delete file:', error);
    }
  };

  const localizeVideo = async (
    file: File,
    locale: string,
    modelType: 'speed' | 'quality'
  ): Promise<{ original: string; translated: string }> => {
    let fileName = '';

    try {
      const { fileUri, fileName: uploadedFileName } =
        await uploadFileToGemini(file);
      fileName = uploadedFileName;

      setStatus('Generating subtitles...');

      const response = await fetch('/api/localize-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileUri,
          mimeType: file.type,
          locale,
          modelType,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to localize video');
      }

      return response.json();
    } finally {
      if (fileName) {
        await deleteFile(fileName);
      }
    }
  };

  const ensureSubtitleFormat = (content: string): string => {
    // Check if it's already in WebVTT format
    if (content.trim().includes('WEBVTT')) {
      const lines = content.trim().split('\n');
      let formattedContent = 'WEBVTT\n\n';
      let currentSubtitle = '';

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line !== '') {
          // Check and format timestamp
          const timestampMatch = line.match(
            /(\d{2}:\d{2}(?::\d{2})?)(\.\d{3})? --> (\d{2}:\d{2}(?::\d{2})?)(\.\d{3})?/
          );
          if (timestampMatch) {
            const start = `${timestampMatch[1]}${timestampMatch[2] || '.000'}`;
            const end = `${timestampMatch[3]}${timestampMatch[4] || '.000'}`;
            currentSubtitle += `${start} --> ${end}\n`;
          } else {
            currentSubtitle += line + '\n\n';
          }
        }
      }

      if (currentSubtitle) {
        formattedContent += currentSubtitle.trim() + '\n';
      }

      return formattedContent.trim();
    }

    // Check if it's in SRT format
    const srtPattern =
      /^\d+\n\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}\n/m;
    if (srtPattern.test(content)) {
      // Convert SRT to WebVTT
      return (
        'WEBVTT\n\n' + content.replace(/(\d{2}:\d{2}:\d{2}),(\d{3})/g, '$1.$2')
      );
    }

    // If it's neither WebVTT nor SRT, attempt to format it as WebVTT
    const lines = content.trim().split('\n');
    let formattedContent = 'WEBVTT\n\n';
    let index = 1;

    for (let i = 0; i < lines.length; i += 3) {
      const timecode = lines[i + 1]?.match(
        /(\d{2}:\d{2}:\d{2})[\.,]?(\d{3})? --> (\d{2}:\d{2}:\d{2})[\.,]?(\d{3})?/
      );
      if (timecode) {
        formattedContent += `${index}\n`;
        formattedContent += `${timecode[1]}.${timecode[2] || '000'} --> ${timecode[3]}.${timecode[4] || '000'}\n`;
        formattedContent += `${lines[i + 2]}\n\n`;
        index++;
      }
    }

    return formattedContent.trim();
  };

  const saveTempSubtitleFile = (content: string): string => {
    // Print the finalized subtitle content
    const formattedContent = ensureSubtitleFormat(content);
    console.log(`Finalized Subtitle Content: ${formattedContent}`);
    const blob = new Blob([formattedContent], { type: 'text/vtt' });
    return URL.createObjectURL(blob);
  };

  const downloadSubtitle = (content: string, isOriginal: boolean = false) => {
    if (!file) return;

    // Get the base filename without extension
    const baseFileName = file.name.replace(/\.[^/.]+$/, '');
    const languageSuffix = isOriginal ? 'original' : locale;
    const fileName = `${baseFileName}_${languageSuffix}.vtt`;

    const formattedContent = ensureSubtitleFormat(content);
    const blob = new Blob([formattedContent], { type: 'text/vtt' });
    const url = URL.createObjectURL(blob);

    // Create a temporary anchor element and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Clean up the URL
    URL.revokeObjectURL(url);

    toast.success(`Downloaded ${fileName}`);
  };

  const uploadVideo = async () => {
    if (!file) return;
    setStatus('Starting video upload and localization...');
    try {
      setSubtitleUrl(null);
      setOriginalSubtitle('');
      setTranslatedSubtitle('');

      const { original, translated } = await localizeVideo(
        file,
        locale,
        modelType
      );
      setOriginalSubtitle(original);
      setTranslatedSubtitle(translated);
      const tempSubtitleUrl = saveTempSubtitleFile(translated);
      setSubtitleUrl(tempSubtitleUrl);
      setStatus('Video localized successfully');
      toast.success('Video localized successfully');
    } catch (error) {
      console.error('Error uploading video:', error);
      setStatus('Error occurred during video localization');
      toast.error(
        error instanceof Error
          ? error.message
          : 'Error occurred during video localization'
      );
    } finally {
      setStatus(''); // Clear the status to re-enable the button
    }
  };

  return (
    <Card className={className}>
      <CardContent className="mt-6">
        <div className="grid w-full items-center gap-4">
          <div className="flex flex-col space-y-1.5">
            <Label htmlFor="videoFile">Video File</Label>
            <Input
              id="videoFile"
              type="file"
              onChange={handleFileChange}
              accept=".mp4,.avi,.mov,.wmv"
            />
          </div>
          <div className="flex flex-col space-y-1.5">
            <Label htmlFor="locale">Target Locale</Label>
            <Select value={locale} onValueChange={setLocale}>
              <SelectTrigger>
                <SelectValue placeholder="Select a locale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en-us">English</SelectItem>
                <SelectItem value="cn-zh">简体中文</SelectItem>
                <SelectItem value="ru-ru">Русский</SelectItem>
                <SelectItem value="ja-jp">日本語</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col space-y-1.5">
            <Label htmlFor="modelType">Model Type</Label>
            <Select
              value={modelType}
              onValueChange={(value) =>
                setModelType(value as 'speed' | 'quality')
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a model type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="speed">Speed</SelectItem>
                <SelectItem value="quality">Quality</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button onClick={uploadVideo} disabled={!file || status !== ''}>
          {status ? 'Processing...' : 'Upload and Localize'}
        </Button>
      </CardFooter>
      {status && (
        <CardContent>
          <p className="text-sm text-gray-500">{status}</p>
        </CardContent>
      )}
      {subtitleUrl && (
        <CardContent>
          <div className="mt-4">
            <h3 className="text-lg font-semibold">
              Video Player with Subtitles
            </h3>
            <video controls className="w-full mt-2">
              <source src={URL.createObjectURL(file!)} type={file!.type} />
              <track kind="captions" src={subtitleUrl} label={locale} default />
              Your browser does not support the video tag.
            </video>
            <div className="mt-4 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadSubtitle(originalSubtitle, true)}
                disabled={!originalSubtitle}
              >
                Download Original Subtitle
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadSubtitle(translatedSubtitle, false)}
                disabled={!translatedSubtitle}
              >
                Download {locale.toUpperCase()} Subtitle
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
