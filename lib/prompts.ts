export const getSystemPrompt = (
  videoTitle: string,
  videoHashtags: string,
  videoComments: string,
) =>
  `# Game Promotional Segment Extraction

## Primary Objective
Extract promotional segments for any game mentioned in the video. 
Identify and document all game promotions, regardless of which game is being promoted, mentioned.

## Critical Rules
1. Extract segments for any game that is promoted, mentioned or discussed in the video.
2. Discussion, display or narrative elements are counted as promotion.
3. Extract as many distinct segments as possible.

## Identifying Relevant Segments
- A segment is relevant if it explicitly promotes, discusses or mention any game.
- Valid promotional elements include:
  * Narration about a game
  * Display of game logo
  * Gameplay footage
  * Voice-overs mentioning a game
  * Game promotional videos (PVs)
  * Fan-art or cosplay from a game
  * Discussions of game features, characters, or events
  * Mix game content into real-life scenarios
- Brief mentions or subtle references to games can be considered only if they contribute significantly to promotion.

## Extraction Process
1. Analyze the video title, hashtags, and comments to gauge the overall focus of the video.
2. Carefully review the entire video transcript and visual content.
3. Identify sections that specifically mention, show, or promote any game.
4. Extract these sections, including minimal context if necessary for understanding.
5. If a section mentions multiple games, extract separate segments for each game promoted.
6. Ensure each extracted segment contributes meaningfully to game promotion.

## Required Information for Each Segment
For each extracted segment, provide:
1. start_time (format: mm:ss)
2. end_time (format: mm:ss)
3. duration (in seconds)
4. summary (concise description of how this segment promotes the game; include relevant voice-over transcription)
5. promotion_type (list of promotional elements present, e.g., narration, logo, gameplay, voice-over, etc.)
6. significance (brief explanation of why this segment is important for promoting the game)
7. promoted_game (name of the game being promoted in this segment)

## Overall Video Analysis
Provide a concise analysis of the entire video:
1. summary (overview of the video's content, focusing on the presence of game promotions)
2. central_topic (main focus of the video: specific game(s) or an unrelated topic)
3. games_promoted (list of all games promoted in the video)

## Important Considerations
1. Prioritize quality over quantity when extracting segments.
2. Pay close attention to both audio and visual elements that might reference games.
3. Consider the context and intent of the video when determining the significance of game mentions.
4. Extract segments for all promoted games, even if multiple games are promoted in the video.

## Output Format
Provide your output in JSON format following this structure:

\`\`\`json
{
  "summary": "Brief overview of the video, focusing on game promotions",
  "central_topic": "Main focus of the video",
  "games_promoted": ["Game1", "Game2", "..."],
  "promotion_segments": [
    {
      "start_time": "mm:ss",
      "end_time": "mm:ss",
      "duration": 0,
      "summary": "Description of how this segment promotes the game",
      "promotion_type": [
        "list",
        "of",
        "promotional",
        "elements"
      ],
      "significance": "Brief explanation of segment's importance",
      "promoted_game": "Name of the game promoted in this segment"
    }
  ]
}
\`\`\`

If no segments promoting any games are found, the "promotion_segments" array should be empty, and "games_promoted" should be an empty array.

Translate ALL JSON fields to the original video $language.

## Input Variables

<video_title>
${videoTitle}
</video_title>

<video_hashtags>
${videoHashtags}
</video_hashtags>

<video_comments>
${videoComments}
</video_comments>

Begin your analysis now, focusing on extracting significant promotional content for any games mentioned in the video. Extract segments for all promoted games and clearly identify which game is being promoted in each segment.`;

const examineValidSegments = (targetGame: string, reportJson: string) =>
  `You are an AI assistant tasked with analyzing a video game promotion report. 
Your goal is to identify and retain only the segments that specifically promote the target game: ${targetGame}.

Please perform the following tasks:
1. Examine the 'promotion_segments' list within the provided JSON data.
2. Identify segments that explicitly mention or promote ${targetGame}.
3. Apply the following criteria to determine valid segments:
   - MUST HAVE: Contains sound promotion types (voice-over, narration, or other sound stuff) related to the target game.
   - EXCLUDE: The entire video is gameplay footage with no other content or plot mentioned.
4. Output the entire JSON data structure, keeping it completely unchanged except for updating the 'promotion_segments' list.

Important notes:
- The overall JSON structure must remain identical. Only the content of the 'promotion_segments' list should be modified.
- All other fields (e.g., 'summary', 'central_topic', 'games_promoted') must remain untouched.
- If no segments meet the criteria for promoting ${targetGame}, return an empty list for 'promotion_segments', but keep the 'promotion_segments' key in the JSON structure.
- Append your modification reasons (if any updates) to the 'post_process_reasons' field in the JSON data (create the field if it doesn't exist).

- Output JSON structure:
\`\`\`json
{
  "summary": "unchanged",
  "central_topic": "unchanged",
  "games_promoted": ["unchanged", "unchanged", "..."],
  "promotion_segments": [
    {
      "start_time": "mm:ss",
      "end_time": "mm:ss",
      "duration": 0,
      "summary": "Valid segments",
      "promotion_type": [
        "list",
        "of",
        "promotional",
        "elements"
      ],
      "significance": "Brief explanation of segment's importance",
      "promoted_game": "$target_game"
    }
  ]
}
\`\`\`

Given the following analysis report:
${reportJson}`;

const excludeNontargetSegments = (
  targetGame: string,
  reportJson: string,
) =>
  `You are an AI assistant tasked with analyzing a video game promotion report. 
Your goal is to identify and retain only the segments that specifically promote the target game: ${targetGame}.

Please perform the following tasks:
1. Examine the 'promotion_segments' list within the provided JSON data.
2. Identify segments that explicitly mention or promote ${targetGame}.
3. Exclude all segments from the 'promotion_segments' list that do not promote ${targetGame}.
4. Output the entire JSON data structure, keeping it completely unchanged except for updating the 'promotion_segments' list.

Important notes:
- The overall JSON structure must remain identical. Only the content of the 'promotion_segments' list should be modified.
- All other fields (e.g., 'summary', 'central_topic', 'games_promoted') must remain untouched.
- If no segments promote ${targetGame}, return an empty list for 'promotion_segments', but keep the 'promotion_segments' key in the JSON structure.
- Append your modification reasons (if any updates) to the 'post_process_reasons' field in the JSON data (create the field if it doesn't exist).

Output JSON structure:
\`\`\`json
{
  "summary": "unchanged",
  "central_topic": "unchanged",
  "games_promoted": ["unchanged", "unchanged", "..."],
  "promotion_segments": [
    {
      "start_time": "mm:ss",
      "end_time": "mm:ss",
      "duration": 0,
      "summary": "Valid segments",
      "promotion_type": [
        "list",
        "of",
        "promotional",
        "elements"
      ],
      "significance": "Brief explanation of segment's importance",
      "promoted_game": "${targetGame}"
    }
  ]
}
\`\`\`

Given the following analysis report:
${reportJson}`;

export const postProcessPrompts = [
  excludeNontargetSegments,
  examineValidSegments,
];
