import axios from "axios";
import { postProcessPrompts } from "@/lib/prompts";
import _ from "lodash";

export type PostProcessReport = {
    name: string;
    report: Record<string, any>;
};

export type PostProcessError = {
    name: string;
    error: string;
};

export const auditVideo = async (
    videoFile: File,
    videoTitle: string,
    videoHashtags: string,
    videoComments: string,
) => {
    const formData = new FormData();
    formData.append("videoFile", videoFile);
    formData.append("videoTitle", videoTitle);
    formData.append("videoHashtags", videoHashtags);
    formData.append("videoComments", videoComments);

    try {
        const response = await axios.post("/api/audit/video", formData, {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });
        return {
            result: response.data.result,
            videoDuration: response.data.videoDuration,
        };
    } catch (error: any) {
        console.error("Error calling audit video API:", error);
        throw error;
    }
};

export const videoAnalysisPostProcess = async (
    targetGame: string,
    reportJson: string,
): Promise<(PostProcessReport | PostProcessError)[]> => {
    const results: (PostProcessReport | PostProcessError)[] = [];
    let processedReport: Record<string, any>;

    try {
        processedReport = JSON.parse(reportJson);
    } catch (error) {
        return [{
            name: targetGame,
            error: "Invalid initial JSON report",
        }];
    }

    for (const promptFunction of postProcessPrompts) {
        const functionName = _.startCase(promptFunction.name);
        const displayName = `[PostProcess] ${functionName} (${targetGame})`;
        try {
            const prompt = promptFunction(
                targetGame,
                JSON.stringify(processedReport),
            );

            const response = await axios.post("/api/audit/post-process", {
                prompt,
            });

            if (response.data.error) {
                results.push({
                    name: displayName,
                    error: response.data.error,
                });
                continue;
            }

            processedReport = response.data.result;
            results.push({
                name: displayName,
                report: processedReport,
            });
        } catch (error: any) {
            console.error("Error during post-processing:", error);
            results.push({
                name: displayName,
                error: error.message || "Unknown error during post-processing",
            });
        }
    }

    return results;
};
