import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import <PERSON> from 'papaparse';
import * as XLSX from 'xlsx';
import { format as fmt } from 'date-fns';
import { saveAs } from 'file-saver';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

export const exportToCSV = (data: any[], fileName: string = 'exported_data.csv') => {
    const csv = Papa.unparse(data);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

    saveAs(blob, fileName);
}

export const exportToXLS = (data: any[], fileName: string = 'exported_data') => {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    const dateStr = fmt(new Date(), 'yyyyMMdd');
    XLSX.utils.book_append_sheet(workbook, worksheet, "Exported");

    // Generate XLS file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    const originalName = `${fileName}${dateStr}.xlsx`;
    saveAs(blob, originalName, { autoBom: true });
}

// Function to sanitize and encode the file name
function sanitizeFileName(name: string): string {
    // Remove any characters that are not allowed in file names
    let safeName = name.replace(/[<>:"/\\|?*\x00-\x1F]/g, '');
    // Replace spaces with underscores
    safeName = safeName.replace(/\s+/g, '_');
    // Encode the file name to handle special characters
    return encodeURIComponent(safeName);
}