export const SAMPLE_FILTER_CONDITIONS = {
    "basic_conditions": {
        "target_region": "jp",
        "target_language": "ja",
        "min_followers": 10000,
        "min_like_view_ratio": 0.05,
        "min_comment_like_ratio": 0.002,
    },
    "video_conditions": {
        "months_to_check": 6,
        "videos_to_check": 20,
        "min_videos_in_period": 9,
        "min_published_days": 7,
        "min_avg_views": 10000,
        "min_avg_likes": 1000,
        "min_avg_comments": 100,
    }
}
export const CREATE_SCOUT_REQUEST = {
    "user_id": "supabase_user_id",
    "search_keywords": ["keyword1", "keyword2"],
    "filter_conditions": SAMPLE_FILTER_CONDITIONS,
    "search_video_count": 20, // Number of videos to search
    "search_sort_type": 1, // Search video sort type: 0-Relatedness, 1-Most likes
    "search_publish_time": 0, // Search video publish type: 0-Unlimited, 1-Last day, 7-Last week, 30-Last month, 90-Last three months, 180-Last half year
}