export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      interview_results: {
        Row: {
          created_at: string
          id: number
          name: string | null
          results: Json | null
          test_type: string | null
        }
        Insert: {
          created_at?: string
          id?: number
          name?: string | null
          results?: Json | null
          test_type?: string | null
        }
        Update: {
          created_at?: string
          id?: number
          name?: string | null
          results?: Json | null
          test_type?: string | null
        }
        Relationships: []
      }
      kol_scout_creator_metrics: {
        Row: {
          created_at: string | null
          creator: string | null
          id: string
          latest_published_at: string | null
          recent_videos_avg_likes: number | null
          recent_videos_avg_views: number | null
          recent_videos_pub_monthly: number | null
          recent_videos_track_count: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          creator?: string | null
          id?: string
          latest_published_at?: string | null
          recent_videos_avg_likes?: number | null
          recent_videos_avg_views?: number | null
          recent_videos_pub_monthly?: number | null
          recent_videos_track_count?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          creator?: string | null
          id?: string
          latest_published_at?: string | null
          recent_videos_avg_likes?: number | null
          recent_videos_avg_views?: number | null
          recent_videos_pub_monthly?: number | null
          recent_videos_track_count?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "kol_scout_creator_metrics_creator_fkey"
            columns: ["creator"]
            isOneToOne: false
            referencedRelation: "kol_scout_creators"
            referencedColumns: ["id"]
          },
        ]
      }
      kol_scout_creators: {
        Row: {
          bio_url: string | null
          category: string | null
          created_at: string | null
          email: string | null
          follower_count: number | null
          id: string
          is_valid: boolean | null
          keyword: string | null
          language: string | null
          latest_published_at: string | null
          nickname: string | null
          profile_url: string | null
          region: string | null
          scout_id: string | null
          sec_uid: string | null
          signature: string | null
          unique_id: string | null
          valid_reason: string | null
          visible_videos_count: number | null
        }
        Insert: {
          bio_url?: string | null
          category?: string | null
          created_at?: string | null
          email?: string | null
          follower_count?: number | null
          id: string
          is_valid?: boolean | null
          keyword?: string | null
          language?: string | null
          latest_published_at?: string | null
          nickname?: string | null
          profile_url?: string | null
          region?: string | null
          scout_id?: string | null
          sec_uid?: string | null
          signature?: string | null
          unique_id?: string | null
          valid_reason?: string | null
          visible_videos_count?: number | null
        }
        Update: {
          bio_url?: string | null
          category?: string | null
          created_at?: string | null
          email?: string | null
          follower_count?: number | null
          id?: string
          is_valid?: boolean | null
          keyword?: string | null
          language?: string | null
          latest_published_at?: string | null
          nickname?: string | null
          profile_url?: string | null
          region?: string | null
          scout_id?: string | null
          sec_uid?: string | null
          signature?: string | null
          unique_id?: string | null
          valid_reason?: string | null
          visible_videos_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "kol_scout_creators_scout_id_fkey"
            columns: ["scout_id"]
            isOneToOne: false
            referencedRelation: "kol_scout_transactions"
            referencedColumns: ["id"]
          },
        ]
      }
      kol_scout_transactions: {
        Row: {
          created_at: string | null
          filter_conditions: Json | null
          id: string
          job_creator: string | null
          search_keywords: string[] | null
          search_publish_time: number | null
          search_sort_type: number | null
          search_video_count: number | null
          status: string | null
          status_code: number | null
          updated_time: string | null
        }
        Insert: {
          created_at?: string | null
          filter_conditions?: Json | null
          id?: string
          job_creator?: string | null
          search_keywords?: string[] | null
          search_publish_time?: number | null
          search_sort_type?: number | null
          search_video_count?: number | null
          status?: string | null
          status_code?: number | null
          updated_time?: string | null
        }
        Update: {
          created_at?: string | null
          filter_conditions?: Json | null
          id?: string
          job_creator?: string | null
          search_keywords?: string[] | null
          search_publish_time?: number | null
          search_sort_type?: number | null
          search_video_count?: number | null
          status?: string | null
          status_code?: number | null
          updated_time?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "kol_scout_transactions_job_creator_fkey"
            columns: ["job_creator"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      kol_scout_videos: {
        Row: {
          comment_count: number | null
          create_time: number | null
          created_at: string | null
          creator_id: string | null
          description: string | null
          duration: number | null
          hashtags: string[] | null
          id: string
          keyword: string | null
          language: string | null
          like_count: number | null
          link: string | null
          region: string | null
          scout_id: string | null
          share_count: number | null
          view_count: number | null
        }
        Insert: {
          comment_count?: number | null
          create_time?: number | null
          created_at?: string | null
          creator_id?: string | null
          description?: string | null
          duration?: number | null
          hashtags?: string[] | null
          id: string
          keyword?: string | null
          language?: string | null
          like_count?: number | null
          link?: string | null
          region?: string | null
          scout_id?: string | null
          share_count?: number | null
          view_count?: number | null
        }
        Update: {
          comment_count?: number | null
          create_time?: number | null
          created_at?: string | null
          creator_id?: string | null
          description?: string | null
          duration?: number | null
          hashtags?: string[] | null
          id?: string
          keyword?: string | null
          language?: string | null
          like_count?: number | null
          link?: string | null
          region?: string | null
          scout_id?: string | null
          share_count?: number | null
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "kol_scout_videos_scout_id_fkey"
            columns: ["scout_id"]
            isOneToOne: false
            referencedRelation: "kol_scout_transactions"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      kol_scout_creator_summary: {
        Row: {
          this_week: number | null
          today: number | null
          total: number | null
        }
        Relationships: []
      }
      kol_scout_transaction_summary: {
        Row: {
          this_week: number | null
          today: number | null
          total: number | null
        }
        Relationships: []
      }
      kol_scout_video_summary: {
        Row: {
          this_week: number | null
          today: number | null
          total: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never
