const kol_scout_videos = `
create table
  public.kol_scout_videos (
    id text not null,
    creator_id text null,
    description text null,
    create_time bigint null,
    duration double precision null,
    view_count bigint null,
    like_count bigint null,
    comment_count bigint null,
    share_count bigint null,
    link text null,
    region text null,
    language text null,
    hashtags text[] null,
    scout_id text null,
    keyword text null,
    created_at timestamp with time zone null default now(),
    constraint kol_scout_videos_pkey primary key (id),
    constraint kol_scout_videos_creator_id_fkey foreign key (creator_id) references kol_scout_creators (id) on delete cascade
  ) tablespace pg_default;
`

const kol_scout_transactions = `
create table
  public.kol_scout_transactions (
    id uuid not null default gen_random_uuid (),
    status text null,
    status_code bigint null,
    updated_time timestamp without time zone null,
    filter_conditions jsonb null,
    search_keywords text[] null,
    search_publish_time bigint null,
    search_sort_type bigint null,
    search_video_count bigint null,
    job_creator uuid null,
    created_at timestamp with time zone null default now(),
    constraint kol_scout_transactions_pkey primary key (id)
  ) tablespace pg_default;
`

const kol_scout_creators = `
create table
  public.kol_scout_creators (
    id text not null,
    sec_uid text null,
    unique_id text null,
    nickname text null,
    follower_count bigint null,
    region text null,
    language text null,
    profile_url text null,
    visible_videos_count bigint null,
    category text null,
    bio_url text null,
    is_valid boolean null,
    valid_reason text null,
    keyword text null,
    signature text null,
    scout_id text null,
    created_at timestamp with time zone null default now(),
    constraint kol_scout_creators_pkey primary key (id)
  ) tablespace pg_default;
`

const kol_scout_creator_metrics = `
    create table
  public.kol_scout_creator_metrics (
    id text not null,
    recent_videos_track_count integer null,
    recent_videos_avg_likes double precision null,
    recent_videos_avg_views double precision null,
    recent_videos_pub_monthly double precision null,
    updated_at timestamp without time zone null default current_timestamp,
    created_at timestamp with time zone null default now(),
    constraint kol_scout_creator_metrics_pkey primary key (id)
  ) tablespace pg_default;
`