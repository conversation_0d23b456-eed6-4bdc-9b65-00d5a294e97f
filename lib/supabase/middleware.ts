import { NextResponse, type NextRequest } from 'next/server'
import { createServer<PERSON>lient } from './server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient()

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const { data: { session } } = await supabase.auth.getSession();

  // Check if the user is logged in
  if (session) {
    // User is logged in
    const { data: { user: userData }, error } = await supabase.auth.getUser()
    if (!error && userData && !userData.user_metadata?.has_set_password) {
      // Password not set, redirect to update-password page
      if (request.nextUrl.pathname !== '/update-password') {
        const url = request.nextUrl.clone()
        url.pathname = '/update-password'
        return NextResponse.redirect(url)
      }
    } else if (request.nextUrl.pathname === '/login') {
      // User is logged in and has set password, redirect to dashboard if trying to access login page
      const url = request.nextUrl.clone()
      url.pathname = '/dashboard'
      return NextResponse.redirect(url)
    }
  } else {
    // User is not logged in
    if (
      !request.nextUrl.pathname.startsWith('/login') &&
      !request.nextUrl.pathname.startsWith('/auth')
    ) {
      // Redirect to the login page
      const url = request.nextUrl.clone()
      url.pathname = '/login'
      console.log(`Redirecting to login page(from ${request.nextUrl.pathname})`)
      return NextResponse.redirect(url)
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}
