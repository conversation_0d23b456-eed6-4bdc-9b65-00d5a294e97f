import * as XLSX from 'xlsx';
import { format as fmt } from 'date-fns';
import { saveAs } from 'file-saver';
import { toast } from 'sonner';

// Type definitions for export data
export interface SignatureExportData {
  Status: 'Success' | 'Failed';
  'Unique ID': string;
  Signature: string;
  'Bio URL': string;
  'Youtube Channel ID': string;
  'Instagram ID': string;
  'Twitter ID': string;
  Email: string;
}

export interface CreatorSignatureInfo {
  unique_id: string;
  signature: string;
  bio_url: string;
  youtube_channel_id: string;
  ins_id: string;
  twitter_id: string;
  email: string;
  extracted_bio_url: string;
  status: 'success' | 'failed';
}

export interface ExportOptions {
  fileName?: string;
  includeTimestamp?: boolean;
  sheetName?: string;
  columns?: string[];
  onProgress?: (progress: number) => void;
}

// Default column configuration
const DEFAULT_COLUMNS = [
  'Status',
  'Unique ID',
  'Signature',
  'Bio URL',
  'Youtube Channel ID',
  'Instagram ID',
  'Twitter ID',
  'Email',
];

/**
 * Transform creator data to export format
 */
function transformCreatorData(
  creator: CreatorSignatureInfo
): SignatureExportData {
  return {
    Status: creator.status === 'success' ? 'Success' : 'Failed',
    'Unique ID': creator.unique_id,
    Signature: creator.signature || '',
    'Bio URL': creator.extracted_bio_url || creator.bio_url || '',
    'Youtube Channel ID': creator.youtube_channel_id || '',
    'Instagram ID': creator.ins_id || '',
    'Twitter ID': creator.twitter_id || '',
    Email: creator.email || '',
  };
}

/**
 * Transform failed creator ID to export format
 */
function transformFailedCreator(creatorId: string): SignatureExportData {
  return {
    Status: 'Failed',
    'Unique ID': creatorId,
    Signature: '',
    'Bio URL': '',
    'Youtube Channel ID': '',
    'Instagram ID': '',
    'Twitter ID': '',
    Email: '',
  };
}

/**
 * Export signature scraper results to Excel with improved error handling and options
 */
export async function exportSignatureScraperResults(
  results: CreatorSignatureInfo[],
  failedCreators: string[],
  options: ExportOptions = {}
): Promise<void> {
  const {
    fileName = 'tiktok_creator_signatures',
    includeTimestamp = true,
    sheetName = 'Creator Signatures',
    columns = DEFAULT_COLUMNS,
    onProgress,
  } = options;

  try {
    // Validate input
    if (results.length === 0 && failedCreators.length === 0) {
      toast.error('No data to export');
      return;
    }

    // Report initial progress
    onProgress?.(10);

    // Transform data with progress reporting
    const totalItems = results.length + failedCreators.length;
    let processedItems = 0;

    const exportData: SignatureExportData[] = [];

    // Process successful results
    for (const result of results) {
      exportData.push(transformCreatorData(result));
      processedItems++;
      onProgress?.(10 + (processedItems / totalItems) * 60);
    }

    // Process failed creators
    for (const creatorId of failedCreators) {
      exportData.push(transformFailedCreator(creatorId));
      processedItems++;
      onProgress?.(10 + (processedItems / totalItems) * 60);
    }

    // Filter columns if specified
    const filteredData =
      columns.length < DEFAULT_COLUMNS.length
        ? exportData.map((row) => {
            const filteredRow: any = {};
            columns.forEach((col) => {
              if (col in row) {
                filteredRow[col] = row[col as keyof SignatureExportData];
              }
            });
            return filteredRow;
          })
        : exportData;

    onProgress?.(70);

    // Create workbook with custom formatting
    const worksheet = XLSX.utils.json_to_sheet(filteredData);

    // Apply column widths for better readability
    const columnWidths = [
      { wch: 10 }, // Status
      { wch: 20 }, // Unique ID
      { wch: 50 }, // Signature
      { wch: 30 }, // Bio URL
      { wch: 25 }, // Youtube Channel ID
      { wch: 20 }, // Instagram ID
      { wch: 20 }, // Twitter ID
      { wch: 30 }, // Email
    ];
    worksheet['!cols'] = columnWidths.slice(0, columns.length);

    onProgress?.(80);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // Add metadata as a second sheet
    const metadata = {
      'Export Date': fmt(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      'Total Records': exportData.length,
      Successful: results.length,
      Failed: failedCreators.length,
      'Success Rate':
        results.length > 0
          ? `${((results.length / totalItems) * 100).toFixed(2)}%`
          : '0%',
    };
    const metadataSheet = XLSX.utils.json_to_sheet([metadata]);
    XLSX.utils.book_append_sheet(workbook, metadataSheet, 'Export Info');

    onProgress?.(90);

    // Generate file
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      compression: true,
    });

    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Generate filename
    const timestamp = includeTimestamp
      ? `_${fmt(new Date(), 'yyyyMMdd_HHmmss')}`
      : '';
    const fullFileName = `${fileName}${timestamp}.xlsx`;

    // Save file
    saveAs(blob, fullFileName);

    onProgress?.(100);

    // Success notification with summary
    toast.success(
      `Exported ${exportData.length} records (${results.length} successful, ${failedCreators.length} failed)`
    );
  } catch (error) {
    console.error('Export failed:', error);
    toast.error(
      `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    throw error;
  }
}

/**
 * Export only successful results
 */
export function exportSuccessfulResults(
  results: CreatorSignatureInfo[],
  options?: ExportOptions
): Promise<void> {
  return exportSignatureScraperResults(results, [], {
    fileName: 'successful_creators',
    ...options,
  });
}

/**
 * Create a summary report of the scraping session
 */
export function generateScrapingSummary(
  results: CreatorSignatureInfo[],
  failedCreators: string[],
  duration: number
): string {
  const total = results.length + failedCreators.length;
  const successRate = total > 0 ? (results.length / total) * 100 : 0;

  const socialMediaStats = results.reduce(
    (acc, creator) => {
      if (creator.youtube_channel_id) acc.youtube++;
      if (creator.ins_id) acc.instagram++;
      if (creator.twitter_id) acc.twitter++;
      if (creator.email) acc.email++;
      if (creator.extracted_bio_url || creator.bio_url) acc.bioUrl++;
      return acc;
    },
    {
      youtube: 0,
      instagram: 0,
      twitter: 0,
      email: 0,
      bioUrl: 0,
    }
  );

  return `
Scraping Summary:
-----------------
Total Processed: ${total}
Successful: ${results.length}
Failed: ${failedCreators.length}
Success Rate: ${successRate.toFixed(2)}%
Duration: ${duration.toFixed(2)} seconds

Social Media Presence:
- YouTube: ${socialMediaStats.youtube} (${((socialMediaStats.youtube / results.length) * 100).toFixed(1)}%)
- Instagram: ${socialMediaStats.instagram} (${((socialMediaStats.instagram / results.length) * 100).toFixed(1)}%)
- Twitter: ${socialMediaStats.twitter} (${((socialMediaStats.twitter / results.length) * 100).toFixed(1)}%)
- Email: ${socialMediaStats.email} (${((socialMediaStats.email / results.length) * 100).toFixed(1)}%)
- Bio/Link URL: ${socialMediaStats.bioUrl} (${((socialMediaStats.bioUrl / results.length) * 100).toFixed(1)}%)
  `.trim();
}
