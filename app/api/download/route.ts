import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import axios from 'axios';

interface AwemeDetail {
  video: {
    download_addr: {
      url_list: string[];
    };
  };
}

interface ResponseData {
  aweme_detail: AwemeDetail;
}

function extractVideoInfo(jsonResponse: any): { url: string | null, awemeId: string | null } {
  try {
    const urlList = jsonResponse.aweme_detail.video.play_addr_h264.url_list;
    const awemeId = jsonResponse.aweme_detail.aweme_id;
    
    if (urlList && urlList.length > 0) {
      return { url: urlList[0], awemeId };
    } else {
      console.error("No download URLs found in the response.");
      return { url: null, awemeId };
    }
  } catch (error) {
    console.error("Error parsing JSON or extracting info:", error);
    return { url: null, awemeId: null };
  }
}

async function downloadTikTokVideo(videoUrl: string): Promise<{ url: string | null, awemeId: string | null }> {
  const options = {
    method: 'GET',
    url: 'https://tokapi-mobile-version.p.rapidapi.com/v1/post',
    params: {
      video_url: videoUrl
    },
    headers: {
      'x-rapidapi-key': process.env.RAPIDAPI_API_KEY,
      'x-rapidapi-host': 'tokapi-mobile-version.p.rapidapi.com'
    }
  };

  try {
    const response = await axios.request(options);
    const downloadedUrl = extractVideoInfo(response.data);
    console.log("Downloaded video URL:", downloadedUrl);
    return downloadedUrl;
  } catch (error) {
    console.error(error);
    return { url: null, awemeId: null };
  }
}

export async function POST(req: NextRequest) {
  const supabase = createServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  const { links } = await req.json();

  if (!links || !Array.isArray(links) || links.length === 0) {
    return new NextResponse('Invalid request', { status: 400 });
  }

  if (links.length > 20) {
    return new NextResponse('Too many links. Maximum allowed is 20.', { status: 400 });
  }

  const downloadInfo = await Promise.all(links.map(downloadTikTokVideo));

  // Filter out null values and return only successful downloads
  const successfulDownloads = downloadInfo.filter((info): info is { url: string, awemeId: string } => 
    info.url !== null && info.awemeId !== null
  );

  return NextResponse.json({ downloadInfo: successfulDownloads });
}
