import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

const apiKey = process.env.GEMINI_API_KEY;
const BASE_URL = 'https://generativelanguage.googleapis.com';

if (!apiKey) {
  throw new Error('GEMINI_API_KEY is not set in environment variables');
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const mimeType = file.type;
    const numBytes = file.size;

    // Create a new FormData for the Gemini API
    const geminiFormData = new FormData();
    geminiFormData.append('file', file);

    const response = await axios.post(
      `${BASE_URL}/upload/v1beta/files?key=${apiKey}`,
      geminiFormData,
      {
        headers: {
          'X-Goog-Upload-Command': 'start, upload, finalize',
          'X-Goog-Upload-Header-Content-Length': numBytes.toString(),
          'X-Goog-Upload-Header-Content-Type': mimeType,
        },
      }
    );

    if (!response.data.file || !response.data.file.uri) {
      throw new Error('Failed to get file URI from response');
    }

    return NextResponse.json({
      fileUri: response.data.file.uri,
      fileName: response.data.file.name,
    });
  } catch (error) {
    console.error('Error uploading file to Gemini:', error);

    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          error: `Upload failed: ${error.response?.statusText || error.message}`,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
