import { NextRequest, NextResponse } from 'next/server';
import {
  GoogleGenerativeAI,
  HarmCategory,
  HarmBlockThreshold,
  GoogleGenerativeAIError,
} from '@google/generative-ai';

const apiKey = process.env.GEMINI_API_KEY;
const geminiModelSpeed = process.env.GEMINI_MODEL_SPEED || 'gemini-1.5-flash';
const geminiModelQuality = process.env.GEMINI_MODEL_QUALITY || 'gemini-1.5-pro';

if (!apiKey) {
  throw new Error('GEMINI_API_KEY is not set in environment variables');
}

const genAI = new GoogleGenerativeAI(apiKey);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fileUri, mimeType, locale, modelType } = body;

    if (!fileUri || !mimeType || !locale || !modelType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const generationConfig = {
      temperature: 1,
      topP: 0.95,
      topK: 64,
      maxOutputTokens: 8192,
    };

    const safetySettings = [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_NONE,
      },
    ];

    const selectedModel =
      modelType === 'speed' ? geminiModelSpeed : geminiModelQuality;
    const model = genAI.getGenerativeModel({
      model: selectedModel,
      generationConfig,
      safetySettings,
    });
    const translateModel = genAI.getGenerativeModel({
      model: geminiModelSpeed,
      generationConfig,
      safetySettings,
    });

    const prompt = `
Generate a transcript from this audio, and provide it in WebVTT format.

<OutputSample>
WEBVTT

00:11.000 --> 00:13.000
We are in New York City

00:13.000 --> 00:16.000
We're actually at the Lucern Hotel, just down the street

00:16.000 --> 00:18.000
from the American Museum of Natural History
</OutputSample>

IMPROTANT:
1. Ignore filler words like "um", "uh", "you know", etc.
2. Keep each subtitle meaningfull, not breaking in the middle
3. Make sure timestamps are in the format MM:SS.mmm
4. Make sure there are line breaks between each subtitle
5. Output only WebVTT text, no html, markdown or anything else

OUTPUT:
Subtitles in WebVTT format:`;

    const result = await model.generateContent([
      {
        text: prompt,
      },
      { fileData: { mimeType, fileUri } },
    ]);

    const subtitleText = result.response.text();

    const translatePrompt = `
Translate this WEBVTT subtitle into ${locale}, make sure keep the timestamp and format. Omit unnecessary words/spaces and keep the translation concise and fluent.

IMPORTANT:
Output only translated WebVTT subtitle, no html, markdown or anything else
`;

    const translatedResult = await translateModel.generateContent([
      {
        text: translatePrompt,
      },
      { text: subtitleText },
    ]);

    const translatedSubtitleText = translatedResult.response.text();

    return NextResponse.json({
      original: subtitleText,
      translated: translatedSubtitleText,
    });
  } catch (error) {
    console.error('Error in localize-video API:', error);

    if (error instanceof GoogleGenerativeAIError) {
      if (error.message.includes('Text not available')) {
        return NextResponse.json(
          { error: 'Text not available, might be due to unsupported content' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to localize video' },
      { status: 500 }
    );
  }
}
