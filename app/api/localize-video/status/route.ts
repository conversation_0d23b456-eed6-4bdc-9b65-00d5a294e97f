import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

const apiKey = process.env.GEMINI_API_KEY;

if (!apiKey) {
  throw new Error('GEMINI_API_KEY is not set in environment variables');
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileUri = searchParams.get('fileUri');

    if (!fileUri) {
      return NextResponse.json(
        { error: 'fileUri parameter is required' },
        { status: 400 }
      );
    }

    const response = await axios.get(`${fileUri}?key=${apiKey}`);

    return NextResponse.json({
      state: response.data.state,
      error: response.data.error,
    });
  } catch (error) {
    console.error('Error checking file status:', error);

    if (axios.isAxiosError(error)) {
      return NextResponse.json(
        {
          error: `Failed to check file state: ${error.response?.statusText || error.message}`,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to check file status' },
      { status: 500 }
    );
  }
}
