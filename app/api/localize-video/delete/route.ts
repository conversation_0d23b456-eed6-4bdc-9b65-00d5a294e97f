import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

const apiKey = process.env.GEMINI_API_KEY;
const BASE_URL = 'https://generativelanguage.googleapis.com';

if (!apiKey) {
  throw new Error('GEMINI_API_KEY is not set in environment variables');
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');

    if (!fileName) {
      return NextResponse.json(
        { error: 'fileName parameter is required' },
        { status: 400 }
      );
    }

    await axios.delete(`${BASE_URL}/v1beta/files/${fileName}?key=${apiKey}`);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting file:', error);

    if (axios.isAxiosError(error)) {
      // Don't treat file deletion errors as critical
      console.error(
        `Failed to delete file: ${error.response?.statusText || error.message}`
      );
    }

    // Return success even if deletion failed to avoid interrupting the main flow
    return NextResponse.json({ success: true });
  }
}
