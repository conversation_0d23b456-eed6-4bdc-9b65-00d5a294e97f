import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    // Trigger the job execution
    const triggerResponse = await fetch(`${process.env.SSS_BE_API_URL}/trigger-scouts?max_jobs=1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json'
      }
    });

    const triggerData = await triggerResponse.json();

    return NextResponse.json({ status: triggerResponse.status, data: triggerData });
  } catch (error) {
    console.error('Error in create-scout route:', error);
    return NextResponse.json({ status: 500, data: { message: 'Internal Server Error' } });
  }
}
