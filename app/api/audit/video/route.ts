import { GoogleGenerativeAI } from '@google/generative-ai';
import {
  FileMetadataResponse,
  GoogleAIFileManager,
} from '@google/generative-ai/server';
import { getSystemPrompt } from '@/lib/prompts';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';

const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY!;
const modeName = process.env.NEXT_PUBLIC_GEMINI_MODEL_SPEED!;

const genAI = new GoogleGenerativeAI(apiKey);
const fileManager = new GoogleAIFileManager(apiKey);

async function uploadToGemini(path: string, mimeType: string) {
  const uploadResult = await fileManager.uploadFile(path, {
    mimeType,
    displayName: path,
  });
  const file = uploadResult.file;
  console.log(`Uploaded file ${file.displayName} as: ${file.name}`);
  return file;
}

async function waitForFilesActive(files: FileMetadataResponse[]) {
  console.log('Waiting for file processing...');
  for (const name of files.map((file) => file.name)) {
    let file = await fileManager.getFile(name);
    while (file.state === 'PROCESSING') {
      process.stdout.write('.');
      await new Promise((resolve) => setTimeout(resolve, 10_000));
      file = await fileManager.getFile(name);
    }
    if (file.state !== 'ACTIVE') {
      throw Error(`File ${file.name} failed to process`);
    }
  }
  console.log('...all files ready\\n');
}

const deleteFile = async (fileName: string): Promise<void> => {
  try {
    await fileManager.deleteFile(fileName);
    console.log(`File ${fileName} deleted successfully`);
  } catch (error) {
    console.log(`Failed to delete file: ${error}`);
  }
};

const generationConfig = {
  temperature: 1,
  topP: 0.95,
  topK: 40,
  maxOutputTokens: 8192,
  responseMimeType: 'application/json',
};

export async function POST(req: NextRequest) {
  let fileName: string | undefined;
  try {
    const formData = await req.formData();

    const videoFile = formData.get('videoFile') as File;
    const videoTitle = formData.get('videoTitle') as string;
    const videoHashtags = formData.get('videoHashtags') as string;
    const videoComments = formData.get('videoComments') as string;

    console.log('------ form data ------');
    console.log(formData);

    if (!videoFile) {
      return NextResponse.json(
        { error: 'Video file is required' },
        { status: 400 }
      );
    }

    const bytes = await videoFile.arrayBuffer();
    const buffer = new Uint8Array(bytes);
    const uploadPath = `./public/uploads/${videoFile.name}`;
    await fs.writeFile(uploadPath, buffer);

    const file = await uploadToGemini(uploadPath, videoFile.type);

    await fs.unlink(uploadPath);
    await waitForFilesActive([file]);

    fileName = file.name;

    const model = genAI.getGenerativeModel({
      model: modeName,
      systemInstruction: getSystemPrompt(
        videoTitle,
        videoHashtags,
        videoComments
      ),
    });

    const chatSession = model.startChat({
      generationConfig,
      history: [
        {
          role: 'user',
          parts: [
            {
              fileData: {
                fileUri: file.uri,
                mimeType: file.mimeType,
              },
            },
          ],
        },
      ],
    });

    const result = await chatSession.sendMessage(`Analyze the video.`);
    const textResult = result.response.text();
    console.log('Text result:', textResult);

    const videoDuration = file.videoMetadata?.videoDuration;
    console.log('Video duration:', videoDuration);

    return NextResponse.json({
      result: textResult,
      videoDuration: videoDuration,
    });
  } catch (error: any) {
    console.error('Error during video audit:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  } finally {
    if (fileName) {
      await deleteFile(fileName);
    }
  }
}
