import { GoogleGenerativeAI } from "@google/generative-ai";
import { NextRequest, NextResponse } from "next/server";

const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY!;
const modeName = process.env.NEXT_PUBLIC_GEMINI_MODEL_SPEED!;
const genAI = new GoogleGenerativeAI(apiKey);

const generationConfig = {
    temperature: 1,
    topP: 0.95,
    topK: 40,
    maxOutputTokens: 8192,
    responseMimeType: "application/json",
};

export async function POST(req: NextRequest) {
    try {
        const { prompt } = await req.json();

        if (!prompt) {
            return NextResponse.json(
                { error: "Prompt is required" },
                { status: 400 },
            );
        }

        const model = genAI.getGenerativeModel({
            model: modeName,
            generationConfig,
        });

        const result = await model.generateContent(prompt);
        const response = result.response;
        const textResult = response.text();

        try {
            // Ensure the response is valid JSON
            const jsonResult = JSON.parse(textResult);
            return NextResponse.json({ result: jsonResult });
        } catch (error) {
            console.error("Invalid JSON response:", textResult);
            return NextResponse.json(
                { error: "Invalid JSON response from AI" },
                { status: 500 },
            );
        }
    } catch (error: any) {
        console.error("Error during post-processing:", error);
        return NextResponse.json(
            { error: error.message || "An unexpected error occurred" },
            { status: 500 },
        );
    }
}
