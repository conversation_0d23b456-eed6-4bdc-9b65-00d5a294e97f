import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const requestData = await req.json();
    const requestJson = JSON.stringify(requestData);

    console.log(`Sending ${requestJson} to scout agent.`)

    const response = await fetch(`${process.env.SSS_BE_API_URL}/create-scout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json'
      },
      body: requestJson,
    });

    const data = await response.json();

    return NextResponse.json({ status: response.status, data })
  } catch (error) {
    return NextResponse.json({ status: 500, data: { message: 'Internal Server Error' } })
  }
}
