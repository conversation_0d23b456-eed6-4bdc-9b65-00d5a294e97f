'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, CreditCard, Activity, Search } from 'lucide-react';
import {
  Metric,
  getTotalVideos,
  getTotalCreators,
  getTotalScouts,
  getTotalCreatorMetrics,
} from './actions';
import { Skeleton } from '@/components/ui/skeleton';

function MetricsCard({
  title,
  value,
  incrementToday,
  incrementThisTweek,
  icon: Icon,
}: {
  title: string;
  value: number;
  incrementToday?: number | string;
  incrementThisTweek?: number | string;
  icon: React.ElementType;
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-1">{value}</div>
        {incrementToday !== undefined && incrementToday !== 0 && (
          <p className="text-xs flex items-center">
            <span className="text-red-500 mr-1">+{incrementToday}</span>
            <span className="text-muted-foreground">from yesterday</span>
          </p>
        )}
      </CardContent>
    </Card>
  );
}

function MetricsCardSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-[100px]" />
        <Skeleton className="h-4 w-4" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-[100px] mb-1" />
        <Skeleton className="h-4 w-[80px]" />
      </CardContent>
    </Card>
  );
}

function MetricCardWrapper({
  fetchFunction,
  icon,
}: {
  fetchFunction: () => Promise<Metric>;
  icon: React.ElementType;
}) {
  const [metric, setMetric] = React.useState<Metric | null>(null);

  React.useEffect(() => {
    fetchFunction().then(setMetric);
  }, [fetchFunction]);

  if (!metric) return <MetricsCardSkeleton />;

  return (
    <MetricsCard
      title={metric.title}
      value={metric.value}
      incrementToday={metric.incrementToday}
      icon={icon}
    />
  );
}

export default function MetricsSection() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <MetricCardWrapper fetchFunction={getTotalVideos} icon={CreditCard} />
      <MetricCardWrapper fetchFunction={getTotalCreators} icon={Users} />
      <MetricCardWrapper fetchFunction={getTotalScouts} icon={Search} />
      <MetricCardWrapper
        fetchFunction={getTotalCreatorMetrics}
        icon={Activity}
      />
    </div>
  );
}
