import React from 'react';
import MetricsSection from './MetricsSection';
import ActivitiesSection from './ActivitiesSection';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
} from '@/components/ui/breadcrumb';

export default function Dashboard() {
  return (
    <div className="container mx-auto space-y-4 px-4">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Scout Metrics</h3>
        <MetricsSection />
      </div>

      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Scout Activities</h3>
        <ActivitiesSection />
      </div>
    </div>
  );
}
