"use client"

import React, { } from 'react';
import { B<PERSON><PERSON><PERSON>b, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

export default function VideoUtils() {

    return (
        <>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem>
                        <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Video Utils</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-3xl font-bold">Video Utils</h2>
                <div className="flex items-center space-x-2">

                </div>
            </div>
            <div className="rounded-md border">
                <p>Welcome to Video Utils. Here you can find various tools for working with videos, including TikTok-specific utilities and video localization.</p>
            </div>
        </>
    );
}
