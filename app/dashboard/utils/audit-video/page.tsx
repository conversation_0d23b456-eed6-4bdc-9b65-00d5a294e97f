"use client";

import PromotionConditionsForm, {
    auditConditonFormSchema,
} from "@/components/PromotionConditionForm";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { z } from "zod";
import {
    auditVideo,
    PostProcessReport,
    videoAnalysisPostProcess,
} from "@/lib/llm_helper";
import { useState } from "react";
import { toast } from "sonner";
import {
    AlertCircle,
    CheckCircle2,
    Clock,
    Shield,
    ShieldCheck,
    ShieldX,
    Target,
    Timer,
    XCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import JsonView from "react18-json-view";
import "react18-json-view/src/style.css";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";

export default function AuditVideoPage() {
    const [jsonReport, setJsonReport] = useState<object>();
    const [postProcessResports, setPostProcessResports] = useState<
        PostProcessReport[]
    >();
    const [requiredMinSegmentLength, setRequiredMinSegmentLength] = useState<
        number | undefined
    >();
    const [
        requiredFirstSegmentPositionPercentage,
        setRequiredFirstSegmentPositionPercentage,
    ] = useState<number | undefined>();
    const [videoDuration, setVideoDuration] = useState<number | undefined>();

    const getAuditResult = () => {
        const lastPostProcessReport = postProcessResports?.findLast(
            (report) => report.report,
        );

        if (lastPostProcessReport) {
            const segments = lastPostProcessReport.report
                .promotion_segments as {
                    start_time: string;
                    duration: number;
                }[];
            const isPromotingTarget = segments.length > 0;
            const totalPromotionDuration = segments.reduce(
                (acc, cur) => acc + cur.duration,
                0,
            );
            
            let firstSegmentAppearInValidPosition = false;
            let startTimeInSeconds = 0;
            let minutes = 0;
            let seconds = 0;
            let minStartTime = 0;
            
            if (isPromotingTarget) {
                const firstSegment = segments[0];
                [minutes, seconds] = firstSegment.start_time.split(":")
                    .map(Number);
                startTimeInSeconds = minutes * 60 + seconds;
                minStartTime = (requiredFirstSegmentPositionPercentage ??
                    0) * (videoDuration ?? 0);

                firstSegmentAppearInValidPosition =
                    startTimeInSeconds < minStartTime;
            }

            return (
                <div className="space-y-6">
                    {/* Overall Validation Status */}
                    <Card>
                        <CardContent className="flex items-center justify-between p-4">
                            <div className="flex items-center gap-3">
                                <div
                                    className={cn(
                                        "p-2 rounded-lg",
                                        (isPromotingTarget &&
                                                firstSegmentAppearInValidPosition)
                                            ? "bg-green-50 dark:bg-green-950/20"
                                            : "bg-red-50 dark:bg-red-950/20",
                                    )}
                                >
                                    {(isPromotingTarget &&
                                            firstSegmentAppearInValidPosition)
                                        ? (
                                            <ShieldCheck className="h-5 w-5 text-green-500" />
                                        )
                                        : (
                                            <ShieldX className="h-5 w-5 text-red-500" />
                                        )}
                                </div>
                                <div className="space-y-1">
                                    <p className="text-sm text-muted-foreground font-medium">
                                        Promotion Status
                                    </p>
                                    <p
                                        className={cn(
                                            "text-base font-semibold",
                                            (isPromotingTarget &&
                                                    firstSegmentAppearInValidPosition)
                                                ? "text-green-600 dark:text-green-400"
                                                : "text-red-600 dark:text-red-400",
                                        )}
                                    >
                                        {(isPromotingTarget &&
                                                firstSegmentAppearInValidPosition)
                                            ? "Valid Promotion"
                                            : "Invalid Promotion"}
                                    </p>
                                </div>
                            </div>
                            <Shield className="h-5 w-5 text-muted-foreground" />
                        </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Clock className="h-5 w-5 text-muted-foreground" />
                                        <h4 className="font-medium">
                                            Promotion Duration
                                        </h4>
                                    </div>
                                    <Target className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div className="mt-4 space-y-2">
                                    <div>
                                        <p className="text-sm text-muted-foreground">
                                            Current
                                        </p>
                                        <p className="text-2xl font-bold">
                                            {totalPromotionDuration} seconds
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">
                                            Target
                                        </p>
                                        <p className="text-xl text-muted-foreground">
                                            {requiredMinSegmentLength
                                                ? `${requiredMinSegmentLength} seconds`
                                                : "Not specified"}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <Timer className="h-5 w-5 text-muted-foreground" />
                                        <h4 className="font-medium">
                                            Segment Position
                                        </h4>
                                    </div>
                                    <Target className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div className="mt-4 space-y-2">
                                    <div>
                                        <p className="text-sm text-muted-foreground">
                                            Current
                                        </p>
                                        {isPromotingTarget
                                            ? (
                                                <>
                                                    <div className="flex items-center gap-2">
                                                        {firstSegmentAppearInValidPosition
                                                            ? (
                                                                <CheckCircle2 className="h-6 w-6 text-green-500" />
                                                            )
                                                            : (
                                                                <XCircle className="h-6 w-6 text-red-500" />
                                                            )}
                                                        <span
                                                            className={cn(
                                                                "text-lg font-medium",
                                                                firstSegmentAppearInValidPosition
                                                                    ? "text-green-500"
                                                                    : "text-red-500",
                                                            )}
                                                        >
                                                            {firstSegmentAppearInValidPosition
                                                                ? "Valid Position"
                                                                : "Invalid Position"}
                                                        </span>
                                                    </div>
                                                    <div className="mt-2 space-y-1">
                                                        <p className="text-sm text-muted-foreground">
                                                            First segment starts
                                                            at:{" "}
                                                            {segments[0]
                                                                .start_time}
                                                            {" "}
                                                            ({Math.round(
                                                                (minutes * 60 +
                                                                    seconds) *
                                                                    100 /
                                                                    videoDuration!,
                                                            )}% of video)
                                                        </p>
                                                        <p className="text-sm text-muted-foreground">
                                                            Required start
                                                            before:{" "}
                                                            {Math.round(
                                                                minStartTime,
                                                            )}{" "}
                                                            seconds
                                                            ({requiredFirstSegmentPositionPercentage}%
                                                            of video)
                                                        </p>
                                                    </div>
                                                </>
                                            )
                                            : (
                                                <p className="text-sm text-muted-foreground">
                                                    No promotion segments
                                                    detected
                                                </p>
                                            )}
                                    </div>
                                    <div>
                                        <p className="text-sm text-muted-foreground">
                                            Target
                                        </p>
                                        <p className="text-muted-foreground">
                                            First{" "}
                                            {requiredFirstSegmentPositionPercentage
                                                ? `${requiredFirstSegmentPositionPercentage}%`
                                                : "0%"} of video
                                            {videoDuration
                                                ? ` (${
                                                    Math.round(
                                                        videoDuration *
                                                            requiredFirstSegmentPositionPercentage! /
                                                            100,
                                                    )
                                                } seconds)`
                                                : ""}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {!isPromotingTarget && (
                        <Card className="bg-yellow-50 dark:bg-yellow-950/20">
                            <CardContent className="pt-6">
                                <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
                                    <AlertCircle className="h-5 w-5" />
                                    <p className="font-medium">
                                        No promotion segments were detected in
                                        this video.
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            );
        }

        return (
            <Card className="bg-red-50 dark:bg-red-950/20">
                <CardContent className="p-4">
                    <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                        <XCircle className="h-5 w-5" />
                        <p className="font-medium">
                            Post process report not found!
                        </p>
                    </div>
                </CardContent>
            </Card>
        );
    };

    const onFormSubmit = async (
        values: z.infer<typeof auditConditonFormSchema>,
    ) => {
        const {
            videoFile,
            targetProduct,
            promotionalDuration,
            positionPercentage,
            videoTitle,
            videoHashtags,
        } = values;

        // Reset states
        setJsonReport(undefined);
        setPostProcessResports(undefined);
        setIsLoading(true);

        setRequiredMinSegmentLength(promotionalDuration);
        setRequiredFirstSegmentPositionPercentage(positionPercentage);

        try {
            // Step 1: Perform video audit
            const { result: jsonResult, videoDuration } = await auditVideo(
                videoFile,
                videoTitle ?? "",
                videoHashtags ?? "",
                "",
            );

            setVideoDuration(videoDuration);

            // Parse and set the initial audit result
            const parsedResult = JSON.parse(jsonResult);
            setJsonReport(parsedResult);

            // Step 2: Perform post-processing
            if (targetProduct) {
                const postProcessResults = await videoAnalysisPostProcess(
                    targetProduct,
                    jsonResult,
                );

                // Filter out successful and error results
                const successResults = postProcessResults.filter(
                    (result): result is PostProcessReport =>
                        !("error" in result),
                );

                setPostProcessResports(successResults);

                // Log any errors that occurred during post-processing
                const errorResults = postProcessResults.filter(
                    (result) => "error" in result,
                );
                if (errorResults.length > 0) {
                    console.error("Post-processing errors:", errorResults);
                }
            }
        } catch (error: any) {
            console.error("Error during video processing:", error);
            toast.error(
                error.message || "An unexpected error occurred",
            );
        } finally {
            setIsLoading(false);
        }
    };

    const [isLoading, setIsLoading] = useState(false);

    return (
        <>
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem>
                        <BreadcrumbLink href="/dashboard">
                            Dashboard
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Utils</BreadcrumbPage>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Audit Video</BreadcrumbPage>
                    </BreadcrumbItem>
                </BreadcrumbList>
            </Breadcrumb>
            <div className="flex-1 space-y-4 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                        Audit Video
                    </h2>
                </div>
            </div>
            <div>
                <PromotionConditionsForm
                    onFormSubmit={onFormSubmit}
                    disabled={isLoading}
                />
            </div>
            <Accordion type="single" collapsible>
                {jsonReport && (
                    <AccordionItem value="audit-result">
                        <AccordionTrigger>Video Audit Result</AccordionTrigger>
                        <AccordionContent>
                            <JsonView src={jsonReport} />
                        </AccordionContent>
                    </AccordionItem>
                )}
                {postProcessResports && (
                    postProcessResports.map((report) => (
                        <AccordionItem key={report.name} value={report.name}>
                            <AccordionTrigger>{report.name}</AccordionTrigger>
                            <AccordionContent>
                                <JsonView src={report.report} />
                            </AccordionContent>
                        </AccordionItem>
                    ))
                )}
                {postProcessResports && (
                    <AccordionItem value={"final-result"}>
                        <AccordionTrigger>Audit Result</AccordionTrigger>
                        <AccordionContent>
                            {getAuditResult()}
                        </AccordionContent>
                    </AccordionItem>
                )}
            </Accordion>
        </>
    );
}
