'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON><PERSON>bI<PERSON>,
  B<PERSON><PERSON><PERSON>bLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import axios from 'axios';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { ChangeEvent, FormEvent, KeyboardEvent, useRef, useState } from 'react';
import { toast } from 'sonner';
import { Textarea } from '@/components/ui/textarea';
import {
  exportSignatureScraperResults,
  generateScrapingSummary,
  type CreatorSignatureInfo as ExportCreatorInfo,
} from '@/lib/export/signature-scraper';
import { ExportDialog } from '@/components/ExportDialog';

// Configuration constants
const MAX_IDS_TO_PROCESS = 1000;
const MAX_PARALLEL_TASKS = 20;
const CACHE_CREATOR_INFO = false;
const LOCAL_STORAGE_KEY = 'tcs_';
const REQUESTS_PER_MINUTE = 100; // Configurable RPM limit
const requestTimestamps: number[] = []; // Track request timestamps

interface QueryResponse {
  user: CreatorSignatureInfo;
  error?: string;
}

interface CreatorSignatureInfo {
  unique_id: string;
  signature: string;
  bio_url: string;
  youtube_channel_id: string;
  ins_id: string;
  twitter_id: string;
  email: string;
  extracted_bio_url: string;
  status: 'success' | 'failed';
}

const enforceRateLimit = async () => {
  const now = Date.now();
  const oneMinuteAgo = now - 60000;

  // Remove timestamps older than 1 minute
  while (requestTimestamps.length > 0 && requestTimestamps[0] < oneMinuteAgo) {
    requestTimestamps.shift();
  }

  // If we've hit the rate limit, wait until we can make another request
  if (requestTimestamps.length >= REQUESTS_PER_MINUTE) {
    const oldestTimestamp = requestTimestamps[0];
    const waitTime = oldestTimestamp - oneMinuteAgo;
    await new Promise((resolve) => setTimeout(resolve, waitTime));
    return enforceRateLimit(); // Recheck after waiting
  }

  // Add current timestamp
  requestTimestamps.push(now);
};

// Helper function to extract email from signature
const extractEmailFromSignature = (signature: string): string => {
  const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
  const match = signature.match(emailRegex);
  return match ? match[0] : '';
};

// Helper function to extract bio URL from signature
const extractBioUrlFromSignature = (signature: string): string => {
  // Look for URLs in the signature
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const matches = signature.match(urlRegex);

  if (matches) {
    // Prioritize bio/link URLs
    for (const url of matches) {
      if (
        url.includes('bio') ||
        url.includes('link') ||
        url.includes('beacons')
      ) {
        return url;
      }
    }
    // Return first URL if no bio-specific URL found
    return matches[0];
  }

  return '';
};

const getInfoByUniqueId = async (
  uniqueId: string
): Promise<CreatorSignatureInfo | null> => {
  // Check local storage first
  if (CACHE_CREATOR_INFO) {
    const cachedData = localStorage.getItem(`${LOCAL_STORAGE_KEY}_${uniqueId}`);
    if (cachedData) {
      const parsed = JSON.parse(cachedData);
      return { ...parsed, status: 'success' };
    }
  }

  const options = {
    method: 'GET',
    url: `https://tokapi-mobile-version.p.rapidapi.com/v1/user/@${uniqueId}`,
    headers: {
      'x-rapidapi-key': process.env.NEXT_PUBLIC_RAPIDAPI_API_KEY,
      'x-rapidapi-host': 'tokapi-mobile-version.p.rapidapi.com',
    },
  };

  try {
    await enforceRateLimit(); // Apply rate limiting
    const response = await axios.request(options);
    const data = response.data as QueryResponse;
    // console.log(data);
    if (!data.error) {
      const signature = data.user.signature || '';
      const userWithStatus = {
        ...data.user,
        status: 'success' as const,
        email: extractEmailFromSignature(signature),
        extracted_bio_url: extractBioUrlFromSignature(signature),
      };

      if (CACHE_CREATOR_INFO) {
        // Save to local storage
        localStorage.setItem(
          `${LOCAL_STORAGE_KEY}_${uniqueId}`,
          JSON.stringify(userWithStatus)
        );
      }

      return userWithStatus;
    }

    return {
      unique_id: uniqueId,
      signature: data.error!,
      bio_url: '',
      youtube_channel_id: '',
      ins_id: '',
      twitter_id: '',
      email: '',
      extracted_bio_url: '',
      status: 'failed' as const,
    };
  } catch (error) {
    console.error(error);
    return null;
  }
};

export default function SignatureScraperPage() {
  const [creatorId, setCreatorId] = useState<string>('');
  const [results, setResults] = useState<CreatorSignatureInfo[]>([]);
  const [failedCreators, setFailedCreators] = useState<string[]>([]);
  const [isScraping, setIsScraping] = useState(false);
  const [progress, setProgress] = useState(0);
  const [idCount, setIdCount] = useState(0);
  const [ids, setIds] = useState<string[]>([]);
  const [scrapingStartTime, setScrapingStartTime] = useState(0);
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Auto size
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const defaultRows = 3;
  const maxRows = 8; // You can set a max number of rows

  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setCreatorId(e.target.value);

    const textarea = e.target;
    textarea.style.height = 'auto';

    const style = window.getComputedStyle(textarea);
    const borderHeight =
      parseInt(style.borderTopWidth) + parseInt(style.borderBottomWidth);
    const paddingHeight =
      parseInt(style.paddingTop) + parseInt(style.paddingBottom);

    const lineHeight = parseInt(style.lineHeight);
    const maxHeight = maxRows
      ? lineHeight * maxRows + borderHeight + paddingHeight
      : Infinity;

    const newHeight = Math.min(textarea.scrollHeight + borderHeight, maxHeight);

    textarea.style.height = `${newHeight}px`;
  };

  const handleInput = (e: FormEvent<HTMLTextAreaElement>) => {
    const creatorId = e.currentTarget.value;
    const newIds = Array.from(
      new Set(
        creatorId
          .split('\n')
          .map((id: string) => id.trim())
          .filter((id) => id !== '')
      )
    ).slice(0, MAX_IDS_TO_PROCESS);
    setIdCount(newIds.length);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      return;
    }
  };

  const handleExport = async () => {
    try {
      await exportSignatureScraperResults(
        results as ExportCreatorInfo[],
        failedCreators,
        {
          fileName: 'tiktok_creator_signatures',
          includeTimestamp: true,
          onProgress: (progress) => {
            // Optional: You could show export progress if needed
            console.log(`Export progress: ${progress}%`);
          },
        }
      );
    } catch (error) {
      // Error is already handled in the export function
      console.error('Export error:', error);
    }
  };

  const handleShowSummary = () => {
    const endTime = Date.now();
    const duration = (endTime - scrapingStartTime) / 1000;
    const summary = generateScrapingSummary(
      results as ExportCreatorInfo[],
      failedCreators,
      duration
    );

    // You could show this in a modal or just log it
    console.log(summary);
    toast.info('Check console for detailed summary');
  };

  const handleClick = async () => {
    if (!creatorId) {
      toast.error('Please enter a creator ID.');
      return;
    }

    const newIds = Array.from(
      new Set(
        creatorId
          .split('\n')
          .map((id: string) => id.trim())
          .filter((id) => id !== '')
      )
    ).slice(0, MAX_IDS_TO_PROCESS);

    setIds(newIds);
    setIsScraping(true);
    setProgress(0);
    setResults([]);

    const scrapedResults: CreatorSignatureInfo[] = [];
    const failedIds: string[] = [];
    const startTime = Date.now();
    setScrapingStartTime(startTime);

    // Parallel processing with concurrency limit
    const processBatch = async (batch: string[], processedCount: number) => {
      const batchResults = await Promise.all(
        batch.map(async (id) => {
          const result = await getInfoByUniqueId(id);

          // Provide detailed toast updates
          if (result) {
            toast.success(`Scraped info for ${id}`);
            return result;
          } else {
            toast.error(`Failed to scrape info for ${id}`);
            failedIds.push(id);
            return null;
          }
        })
      );

      // Update progress after entire batch is complete
      const newProcessedCount = processedCount + batch.length;
      setProgress((newProcessedCount / newIds.length) * 100);
      const newResults = batchResults.filter(
        (result) => result !== null
      ) as CreatorSignatureInfo[];
      scrapedResults.push(...newResults);
      setResults(scrapedResults);

      return newResults;
    };

    try {
      let processedCount = 0;
      for (let i = 0; i < newIds.length; i += MAX_PARALLEL_TASKS) {
        const batch = newIds.slice(i, i + MAX_PARALLEL_TASKS);
        await processBatch(batch, processedCount);
        processedCount += batch.length;
      }

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      setFailedCreators(failedIds);

      if (scrapedResults.length > 0) {
        toast.success(
          `Scraped ${scrapedResults.length}/${newIds.length} creators successfully in ${duration.toFixed(
            2
          )} seconds`
        );
      } else {
        toast.error('No creators could be scraped');
      }
    } catch (error) {
      toast.error('An error occurred during scraping');
    } finally {
      setIsScraping(false);
      setProgress(100);
    }
  };

  return (
    <>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Utils</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Signature Scraper</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            Tiktok Creator Signature Scraper
          </h2>
        </div>
        <div className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="creator-id">
              Creator IDs{' '}
              {idCount > 0 && `(${idCount} / ${MAX_IDS_TO_PROCESS})`}
            </Label>
            <Textarea
              id="creator-id"
              placeholder="Enter creator IDs (one per line)"
              value={creatorId}
              ref={textareaRef}
              onChange={handleChange}
              onInput={handleInput}
              onKeyDown={(e) => handleKeyDown(e)}
              rows={defaultRows}
              className="min-h-[none] resize-none"
              disabled={isScraping}
            />
            <p className="text-sm text-muted-foreground">
              Tip: Enter up to {MAX_IDS_TO_PROCESS} unique IDs, one per line.
              Results will be cached for faster future lookups.
            </p>
          </div>
          {isScraping && (
            <div className="space-y-2">
              <Progress value={progress} />
              <p className="text-sm text-muted-foreground">
                Scraping progress: {progress.toFixed(0)}% ({results.length}/
                {ids.length} creators)
              </p>
            </div>
          )}
          <div className="grid grid-cols-2 gap-4">
            <Button
              onClick={handleClick}
              className="w-full"
              disabled={isScraping}
            >
              {isScraping ? 'Scraping...' : 'Scrape'}
            </Button>
            <Button
              onClick={handleExport}
              variant="secondary"
              className="w-full"
              disabled={results.length === 0 && failedCreators.length === 0}
            >
              Export Results
            </Button>
          </div>

          {(results.length > 0 || failedCreators.length > 0) && (
            <div className="mt-4">
              {results.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-xl font-semibold mb-2">
                    Results ({results.length} creators, {failedCreators.length}{' '}
                    failed)
                  </h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Status</TableHead>
                        <TableHead>Tiktok</TableHead>
                        <TableHead>Bio/Link</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Youtube</TableHead>
                        <TableHead>Instagram</TableHead>
                        <TableHead>Twitter</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {results.slice(0, 100).map((result, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-semibold ${
                                result.status === 'success'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}
                            >
                              {result.status === 'success'
                                ? 'Success'
                                : 'Failed'}
                            </span>
                          </TableCell>
                          <TableCell>
                            <a
                              href={`https://www.tiktok.com/@${result.unique_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {result.unique_id}
                            </a>
                          </TableCell>
                          <TableCell>
                            {(result.extracted_bio_url || result.bio_url) && (
                              <a
                                href={
                                  result.extracted_bio_url || result.bio_url
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                Link
                              </a>
                            )}
                          </TableCell>
                          <TableCell>
                            {result.email && (
                              <a
                                href={`mailto:${result.email}`}
                                className="text-blue-600 hover:underline"
                              >
                                {result.email}
                              </a>
                            )}
                          </TableCell>
                          <TableCell>
                            {result.youtube_channel_id && (
                              <a
                                href={`https://www.youtube.com/channel/${result.youtube_channel_id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                @{result.youtube_channel_id}
                              </a>
                            )}
                          </TableCell>
                          <TableCell>
                            {result.ins_id && (
                              <a
                                href={`https://instagram.com/${result.ins_id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                @{result.ins_id}
                              </a>
                            )}
                          </TableCell>
                          <TableCell>
                            {result.twitter_id && (
                              <a
                                href={`https://twitter.com/${result.twitter_id}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                @{result.twitter_id}
                              </a>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Export Dialog */}
      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        results={results as ExportCreatorInfo[]}
        failedCreators={failedCreators}
      />
    </>
  );
}
