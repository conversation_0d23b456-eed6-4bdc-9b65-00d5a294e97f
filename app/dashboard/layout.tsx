import React from 'react';
import Link from 'next/link';
import { Squirrel } from 'lucide-react';
import Sidebar from '@/components/Sidebar';
import { ModeToggle } from '@/components/ModeToggle';
import { createServerClient } from '@/lib/supabase/server';

export default async function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const supabase = createServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    // Redirect to login or show an error
    return <div>Please log in to access the dashboard.</div>;
  }

  return (
    <>
      <div className="flex h-full relative">
        <div className="hidden md:block w-52 bg-muted/40 fixed top-0 bottom-0 left-0 overflow-y-auto">
          <Sidebar isVisible={true} />
        </div>
        <div className="flex flex-col flex-1 overflow-hidden md:ml-52">
          <header className="flex h-14 items-center gap-4 border-b bg-muted/40 px-6 md:hidden min-h-[60px] justify-between">
            <Sidebar isVisible={false} />
            <Link
              href="/dashboard"
              className="flex items-center gap-2 font-semibold"
            >
              <Squirrel className="h-6 w-6" />
              <span className="">SSS</span>
            </Link>
            <ModeToggle />
          </header>
          <main className="flex-1 overflow-y-auto">
            <div className="p-6">{children}</div>
          </main>
        </div>
      </div>
    </>
  );
}
