'use client';

import React, {
  useState,
  useCallback,
  useMemo,
  useEffect,
  useRef,
} from 'react';
import useSWR from 'swr';
import {
  Loader2,
  X,
  Columns3,
  FileDown,
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  Check,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Button, buttonVariants } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import FilterOptions from '@/components/FilterOptions';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { exportToXLS } from '@/lib/utils';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { createClient } from '@/lib/supabase/client';

interface Creator {
  id: string;
  sec_uid: string | null;
  unique_id: string | null;
  nickname: string | null;
  follower_count: number | null;
  region: string | null;
  language: string | null;
  profile_url: string | null;
  visible_videos_count: number | null;
  category: string | null;
  bio_url: string | null;
  is_valid: boolean | null;
  valid_reason: string | null;
  keyword: string | null;
  signature: string | null;
  scout_id: string | null;
  created_at: string | null;
  email: string | null;
  latest_published_at: string | null;
}

export default function KOLScoutCreators() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [filters, setFilters] = useState<Record<string, string>>({
    nickname: '',
    region: '',
    scoutId: '',
    minFollower: '',
    minVideos: '',
    isValid: '',
    hasEmail: '',
    scoutedAfter: '',
  });

  useEffect(() => {
    try {
      const savedFilters = localStorage.getItem(
        'creatorFilters:/dashboard/scout/creators'
      );
      if (savedFilters) {
        const parsedFilters = JSON.parse(savedFilters);
        setFilters(parsedFilters);
        applyFilters(parsedFilters);
      }
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }

    const scout_id = searchParams?.get('scout_id');
    if (scout_id) {
      setFilters((prev) => ({ ...prev, scoutId: scout_id }));
    }
  }, [searchParams]);

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    id: false,
    sec_uid: false,
    unique_id: false,
    nickname: true,
    follower_count: true,
    region: true,
    language: false,
    profile_url: false,
    visible_videos_count: true,
    category: false,
    bio_url: false,
    is_valid: true,
    valid_reason: false,
    keyword: false,
    signature: false,
    scout_id: false,
    created_at: false,
    email: true,
    latest_published_at: true,
  });

  const filterFields = [
    { id: 'nickname', label: 'Nickname', placeholder: 'Filter nicknames...' },
    { id: 'region', label: 'Region', placeholder: 'Filter region...' },
    { id: 'scoutId', label: 'Scout ID', placeholder: 'Filter Scout ID...' },
    {
      id: 'minFollower',
      label: 'Min Followers',
      placeholder: 'Min followers...',
      type: 'number',
    },
    {
      id: 'minVideos',
      label: 'Min Videos',
      placeholder: 'Min videos...',
      type: 'number',
    },
    {
      id: 'isValid',
      label: 'Is Valid',
      placeholder: 'Select validity',
      type: 'select',
      options: [
        { value: undefined, label: 'All' },
        { value: 'true', label: 'Valid' },
        { value: 'false', label: 'Invalid' },
      ],
    },
    {
      id: 'hasEmail',
      label: 'Has Email',
      placeholder: 'Select email status',
      type: 'select',
      options: [
        { value: undefined, label: 'All' },
        { value: 'true', label: 'Has Email' },
        { value: 'false', label: 'No Email' },
      ],
    },
    {
      id: 'scoutedAfter',
      label: 'Scouted After',
      placeholder: 'YYYY-MM-DD HH:MM:SS',
      type: 'datetime-local',
    },
  ];
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [isExporting, setIsExporting] = useState(false);
  const exportAllRef = useRef<AbortController | null>(null);

  function applyFiltersQuery(query: any) {
    if (filters.nickname && filters.nickname !== '') {
      query = query.ilike('nickname', `%${filters.nickname}%`);
    }

    if (filters.region && filters.region !== '') {
      query = query.ilike('region', filters.region);
    }

    if (filters.scoutId && filters.scoutId !== '') {
      query = query.eq('scout_id', filters.scoutId);
    }

    if (filters.minFollower && filters.minFollower !== '') {
      query = query.gte('follower_count', parseInt(filters.minFollower));
    }

    if (filters.minVideos && filters.minVideos !== '') {
      query = query.gte('visible_videos_count', parseInt(filters.minVideos));
    }

    if (filters.isValid && filters.isValid !== '') {
      query = query.eq('is_valid', filters.isValid === 'true');
    }

    if (filters.hasEmail && filters.hasEmail !== '') {
      if (filters.hasEmail === 'true') {
        query = query.not('email', 'is', null);
      } else {
        query = query.is('email', null);
      }
    }

    if (filters.scoutedAfter && filters.scoutedAfter !== '') {
      query = query.gte('created_at', filters.scoutedAfter);
    }

    if (sorting.length > 0) {
      const { id, desc } = sorting[0];
      query = query.order(id, { ascending: !desc });
      console.log('Sorting:', id, desc);
    }

    return query;
  }

  const fetchCreators = useCallback(async () => {
    const supabase = createClient();
    let query = supabase
      .from('kol_scout_creators')
      .select('*', { count: 'exact' })
      .range(
        pagination.pageIndex * pagination.pageSize,
        (pagination.pageIndex + 1) * pagination.pageSize - 1
      );

    query = applyFiltersQuery(query);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return { creators: data || [], totalCount: count || 0 };
  }, [pagination.pageIndex, pagination.pageSize, filters, sorting]);

  const { data, error, isLoading, mutate } = useSWR(
    ['creators', pagination, filters, sorting],
    fetchCreators
  );

  const creators = data?.creators || [];
  const totalCount = data?.totalCount || 0;
  const pageCount = Math.ceil(totalCount / pagination.pageSize);

  const applyFilters = useCallback(
    (newFilters: Record<string, string>) => {
      // console.log('Applying filters:', newFilters)
      setFilters(newFilters);
      localStorage.setItem(
        'creatorFilters:/dashboard/scout/creators',
        JSON.stringify(newFilters)
      );
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      mutate();
    },
    [mutate, setFilters]
  );

  useEffect(() => {
    const scout_id = searchParams?.get('scout_id');
    if (scout_id) {
      handleFilterChange('scoutId', scout_id);
    }
  }, []);

  const fetchAllCreators = useCallback(async () => {
    const supabase = createClient();
    let allCreators: Creator[] = [];
    let page = 0;
    const pageSize = 1000;
    let hasMore = true;

    while (hasMore) {
      let query = supabase
        .from('kol_scout_creators')
        .select(
          `
        *
      `
        )
        .range(page * pageSize, (page + 1) * pageSize - 1);

      query = applyFiltersQuery(query);

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      if (data && data.length > 0) {
        const flattenedCreators = data.map((creator) => {
          const flattenedCreator = {
            ...creator,
          };
          const { ...creatorWithoutMetrics } = flattenedCreator;
          return creatorWithoutMetrics;
        });

        allCreators = [...allCreators, ...flattenedCreators];
        page++;

        if (data.length < pageSize) {
          hasMore = false;
        }
      } else {
        hasMore = false;
      }
    }

    return allCreators;
  }, [filters, sorting]);

  const createSortableColumn = useCallback(
    (
      accessorKey: keyof Creator,
      header: string,
      alignment: 'start' | 'end' = 'start',
      cellRenderer?: (value: any) => React.ReactNode
    ): ColumnDef<Creator> => ({
      accessorKey,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant={isSorted ? 'secondary' : 'ghost'}
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className={`w-full justify-${alignment} px-4 py-2`}
          >
            <span className="mr-2">{header}</span>
            {isSorted === 'asc' && <ChevronUp className="h-4 w-4" />}
            {isSorted === 'desc' && <ChevronDown className="h-4 w-4" />}
            {!isSorted && <ChevronsUpDown className="h-4 w-4" />}
          </Button>
        );
      },
      enableSorting: true,
      cell: ({ row }) => {
        const value = row.getValue(accessorKey);
        const cellContent = (() => {
          if (accessorKey === 'created_at') {
            return new Date(value as string).toLocaleString();
          }
          if (accessorKey === 'is_valid') {
            return (
              <Badge variant={value ? 'outline' : 'destructive'}>
                {value ? (
                  <>
                    <Check className="mr-1 h-4 w-4" /> Valid
                  </>
                ) : (
                  <>
                    <X className="mr-1 h-4 w-4" /> Invalid
                  </>
                )}
              </Badge>
            );
          }
          if (accessorKey === 'bio_url') {
            if (!value) {
              return null;
            }
            return (
              <Link
                href={value as string}
                className={buttonVariants({ variant: 'outline' })}
                target="_blank"
                rel="noopener noreferrer"
              >
                View
              </Link>
            );
            // return <Button variant='outline' size="sm" href={value as string} target="_blank">View</Button>
          }
          if (typeof value === 'string' && value.length > 50) {
            return (
              <div className="max-w-[350px] truncate" title={value}>
                {value}
              </div>
            );
          }
          return value as string | number | boolean;
        })();
        return (
          <div className={`text-${alignment} px-4 py-2`}>
            {cellRenderer ? cellRenderer(value) : cellContent}
          </div>
        );
      },
    }),
    []
  );

  const createUnsortableColumn = useCallback(
    (
      accessorKey: keyof Creator,
      header: string,
      alignment: 'start' | 'end' = 'start',
      cellRenderer?: (value: any) => React.ReactNode
    ): ColumnDef<Creator> => ({
      accessorKey,
      header: () => (
        <Button
          variant="ghost"
          className={`w-full justify-${alignment} px-4 py-2`}
          disabled
        >
          <span className="mr-2">{header}</span>
        </Button>
      ),
      enableSorting: false,
      cell: ({ row }) => {
        const value = row.getValue(accessorKey);
        const cellContent = (() => {
          if (accessorKey === 'created_at') {
            return new Date(value as string).toLocaleString();
          }
          if (typeof value === 'string' && value.length > 50) {
            return (
              <div className="max-w-[350px] truncate" title={value}>
                {value}
              </div>
            );
          }
          return value as string | number | boolean;
        })();
        return (
          <div className={`text-${alignment} px-4 py-2`}>
            {cellRenderer ? cellRenderer(value) : cellContent}
          </div>
        );
      },
    }),
    []
  );

  const columns = useMemo<ColumnDef<Creator>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      createSortableColumn('id', 'ID'),
      createSortableColumn('nickname', 'Nickname'),
      createSortableColumn('is_valid', 'Valid'),
      createSortableColumn('valid_reason', 'Valid Reason'),
      createSortableColumn('region', 'Region'),
      createSortableColumn('follower_count', 'Followers'),
      createSortableColumn('language', 'Language'),
      createSortableColumn('visible_videos_count', 'Videos'),
      createSortableColumn('category', 'Category'),
      createSortableColumn('bio_url', 'Bio URL'),
      createSortableColumn(
        'email',
        'Email',
        'start',
        (value) =>
          value && (
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(value as string);
                  toast.success('Email copied to clipboard');
                }}
              >
                {value}
              </Button>
            </div>
          )
      ),
      createSortableColumn('signature', 'Signature'),
      createSortableColumn('created_at', 'Created At'),
      createUnsortableColumn('latest_published_at', 'Latest Pub', 'start'),
      {
        id: 'actions',
        enableHiding: false,
        cell: ({ row }) => {
          const creator = row.original;

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    navigator.clipboard.writeText(creator.id);
                    toast.success(
                      `Creator ID ${creator.id} copied to clipboard`
                    );
                  }}
                >
                  Copy creator ID
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    if (creator.profile_url) {
                      window.open(creator.profile_url, '_blank');
                      toast.success('Creator profile opened in new tab');
                    } else {
                      toast.error('No profile URL available');
                    }
                  }}
                >
                  Open creator profile
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [createSortableColumn, createUnsortableColumn]
  );

  const table = useReactTable({
    data: creators,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    manualPagination: true,
    manualSorting: true,
    pageCount,
  });

  const handleFilterChange = useCallback(
    (key: string, value: string) => {
      setFilters((prev) => {
        const newFilters = { ...prev, [key]: value };
        localStorage.setItem(
          'creatorFilters:/dashboard/scout/creators',
          JSON.stringify(newFilters)
        );
        return newFilters;
      });
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      mutate();
    },
    [mutate]
  );

  return (
    <>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/scout">KOL Scout</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Scouted Creators</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold">Scouted Creators</h3>
        <div className="flex space-x-2">
          <FilterOptions
            filters={filters}
            setFilters={setFilters}
            applyFilters={applyFilters}
            filterFields={filterFields}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Columns3 className="mr-2 h-4 w-4" />
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="text-red-500">
                <FileDown className="mr-2 h-4 w-4" />
                Export <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  const selectedRows = table.getFilteredSelectedRowModel().rows;
                  if (selectedRows.length === 0) {
                    toast.error('No rows selected for export');
                    return;
                  }
                  const fileName = 'ScoutedCreators';
                  const allCreators = selectedRows.map((row) => row.original);
                  exportToXLS(allCreators, fileName);
                  toast.success(
                    `Exported ${selectedRows.length} rows to Excel`
                  );
                }}
              >
                Export SELECTED (
                {table.getFilteredSelectedRowModel().rows.length}) rows
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={async () => {
                  if (isExporting) {
                    toast.error('An export is already in progress');
                    return;
                  }
                  setIsExporting(true);
                  exportAllRef.current = new AbortController();
                  try {
                    const fileName = 'ScoutedCreators';
                    const allCreators = await fetchAllCreators();
                    if (exportAllRef.current?.signal.aborted) {
                      return;
                    }
                    exportToXLS(allCreators, fileName);
                    toast.success(
                      `Exported all ${allCreators.length} rows to Excel`
                    );
                  } catch (error: any) {
                    if (error?.name === 'AbortError') {
                      toast.info('Export cancelled');
                    } else {
                      console.error('Error fetching all creators:', error);
                      toast.error(
                        'Failed to export all creators. Please try again.'
                      );
                    }
                  } finally {
                    setIsExporting(false);
                    exportAllRef.current = null;
                  }
                }}
                disabled={isExporting}
              >
                {isExporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>Export ALL ({totalCount}) rows</>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {Object.entries(filters).some(([_, value]) => value !== '') && (
        <>
          <Separator className="mb-2" />
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(
              ([key, value]) =>
                value && (
                  <Badge key={key} variant="secondary" className="text-sm">
                    {key}:{' '}
                    {key === 'isValid'
                      ? value === 'true'
                        ? 'Yes'
                        : 'No'
                      : value}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 ml-2"
                      onClick={() => handleFilterChange(key, '')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )
            )}
          </div>
          <div className="m-2"></div>
        </>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="w-40 text-sm text-muted-foreground hidden lg:block">
          {table.getFilteredSelectedRowModel().rows.length} of {totalCount}{' '}
          selected
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.firstPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <p className="text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of {pageCount}
          </p>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.lastPage()}
            disabled={!table.getCanNextPage()}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Page size</p>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              const newPageSize = Number(e.target.value);
              table.setPageSize(newPageSize);
            }}
            className="h-8 w-20 rounded-md border border-input bg-background px-2"
          >
            {[10, 50, 200].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
        </div>
      </div>
    </>
  );
}
