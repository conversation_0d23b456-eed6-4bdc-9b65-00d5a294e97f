'use client';

import React, { FormE<PERSON>, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import SwitchableCondition from '@/components/SwitchableCondition';
import MultipleSelector from '@/components/MultipleSelector';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  <PERSON>readcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  <PERSON>readcrumbList,
  <PERSON>read<PERSON>rumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { createClient } from '@/lib/supabase/client';

const formSchema = z.object({
  search_keywords: z
    .array(z.string())
    .min(1, 'At least one keyword is required')
    .max(7, 'Maximum of 7 keywords allowed'),
  search_video_count: z.number().min(1).nullable(),
  search_sort_type: z.enum(['0', '1']).nullable(),
  search_publish_time: z.enum(['0', '1', '7', '30', '90', '180']).nullable(),
  basic_conditions_enabled: z.boolean(),
  basic_conditions: z.object({
    target_region: z.string().nullable(),
    // target_language: z.string().nullable(),
    min_followers: z.number().min(0).nullable(),
    min_like_view_ratio: z.number().min(0).max(1).nullable(),
    min_comment_like_ratio: z.number().min(0).max(1).nullable(),
  }),
  video_conditions_enabled: z.boolean(),
  video_conditions: z.object({
    months_to_check: z.number().min(1).max(12).nullable(),
    videos_to_check: z.number().min(1).nullable(),
    min_videos_in_period: z.number().min(1).nullable(),
    min_published_days: z.number().min(1).nullable(),
    min_avg_views: z.number().min(0).nullable(),
    min_avg_likes: z.number().min(0).nullable(),
    // min_avg_comments: z.number().min(0).nullable(),
  }),
});

export default function CreateScoutPage() {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formValues, setFormValues] = useState<z.infer<
    typeof formSchema
  > | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await form.handleSubmit(onSubmit)(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const initialValues = useMemo(() => {
    let defaultValues: z.infer<typeof formSchema> = {
      search_keywords: [],
      search_video_count: 20,
      search_sort_type: '1',
      search_publish_time: '0',
      basic_conditions_enabled: true,
      basic_conditions: {
        target_region: 'jp',
        min_followers: 10000,
        min_like_view_ratio: 0.05,
        min_comment_like_ratio: 0.002,
      },
      video_conditions_enabled: true,
      video_conditions: {
        months_to_check: 6,
        videos_to_check: 20,
        min_videos_in_period: 9,
        min_published_days: 7,
        min_avg_views: 10000,
        min_avg_likes: 1000,
      },
    };

    if (typeof window !== 'undefined') {
      const savedConfig = localStorage.getItem('scoutCreationConfig');
      if (savedConfig) {
        const configObject = JSON.parse(savedConfig);
        defaultValues = { ...defaultValues, ...configObject };
      }
    }

    return defaultValues;
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    values: initialValues,
  });

  useEffect(() => {
    const subscription = form.watch((value) =>
      saveFormState(value as z.infer<typeof formSchema>)
    );
    return () => subscription.unsubscribe();
  }, [form, initialValues]);

  const saveFormState = (values: z.infer<typeof formSchema>) => {
    localStorage.setItem('scoutCreationConfig', JSON.stringify(values));
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setFormValues(values);
    setIsDialogOpen(true);
  }

  const supabase = createClient();

  async function createNewScoutRecord(requestData: string) {
    const requestJson = JSON.parse(requestData);

    requestJson['status_code'] = 0;
    requestJson['status'] = 'pending';

    // Insert the kol_scout_transaction record
    const { data, error } = await supabase
      .from('kol_scout_transactions')
      .insert(requestJson);

    console.info(`Inserted new scout job to db: ${requestData}`);

    if (error) {
      console.error('Error inserting kol_scout_transaction:', error);
    }
  }

  async function confirmSubmit() {
    if (!formValues) return;
    setIsSubmitting(true);

    try {
      const filterNullValues = (obj: any): any => {
        if (obj === null || typeof obj !== 'object') {
          return obj;
        }

        if (Array.isArray(obj)) {
          return obj.map(filterNullValues).filter((v) => v != null);
        }

        return Object.fromEntries(
          Object.entries(obj)
            .filter(([_, v]) => v != null)
            .map(([k, v]) => {
              if (!v) {
                return [k, v];
              }
              if (typeof v === 'object' && 'value' in v) {
                return [k, v.value ?? null];
              }
              return [k, typeof v === 'object' ? filterNullValues(v) : v];
            })
        );
      };

      // Convert this to string array ["xx", "yy"]
      const search_keywords = Object.values(formValues.search_keywords);
      const originalRequestObject = {
        search_keywords: search_keywords,
        search_video_count: formValues.search_video_count,
        search_sort_type: formValues.search_sort_type,
        search_publish_time: formValues.search_publish_time,
        filter_conditions: {
          basic_conditions: formValues.basic_conditions_enabled
            ? formValues.basic_conditions
            : undefined,
          video_conditions: formValues.video_conditions_enabled
            ? formValues.video_conditions
            : undefined,
        },
      };

      // console.log(`originalRequestObject: ${JSON.stringify(originalRequestObject)}`);

      const requestData = filterNullValues(originalRequestObject);

      // console.log(`requestData: ${JSON.stringify(requestData)}`);

      // const { data: { user } } = await supabase.auth.getUser();
      // requestData['job_creator'] = user?.id;

      const requestJson = JSON.stringify(requestData);
      toast.info('Scout creation initiated');

      try {
        // const endpoint = '/api/create-scout'
        const endpoint = '/api/trigger-scout';
        createNewScoutRecord(requestJson);

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: requestJson,
        });

        const data = await response.json();

        if (response.ok) {
          toast.success('Scout creation request submitted successfully');
          router.push('/dashboard/scout');
        } else {
          toast.error(
            `Failed to submit scout creation request: ${data.message}`
          );
          console.error('Failed to submit scout creation request:', data);
        }
      } catch (fetchError) {
        toast.error('Network error occurred');
        console.error('Network error:', fetchError);
      }
    } catch (error) {
      toast.error('Failed to create scout');
      console.error('Error creating scout:', error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleEnterKey = (event: React.KeyboardEvent<HTMLElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      const target = event.target as HTMLElement;

      if (
        target.tagName === 'BUTTON' &&
        target.getAttribute('type') === 'submit'
      ) {
        // If it's the submit button, allow the form submission
        return;
      }

      // Check if the target is within a MultipleSelector component
      const multipleSelectorElement = target.closest('.multiple-selector');
      if (multipleSelectorElement) {
        // If it's within a MultipleSelector, don't do anything (let the component handle it)
        return;
      }

      // Simulate Tab key press
      const focusableElements =
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
      const form = target.closest('form');
      if (form) {
        const focusable = Array.from(
          form.querySelectorAll(focusableElements)
        ) as HTMLElement[];
        const index = focusable.indexOf(target);
        if (index > -1) {
          let nextElement = focusable[index + 1] || focusable[0];
          nextElement.focus();
        }
      }
    }
  };

  return (
    <>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/scout">KOL Scout</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>New Scout</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <h3 className="text-2xl font-bold h-10 flex items-center">New Scout</h3>
      <Form {...form}>
        <form
          onSubmit={handleSubmit}
          className="space-y-6 pb-6"
          onKeyDown={handleEnterKey}
        >
          <fieldset disabled={isSubmitting}>
            <FormField
              control={form.control}
              name="search_keywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Keywords</FormLabel>
                  <FormControl>
                    <div className="multiple-selector">
                      <MultipleSelector
                        value={field.value.map((keyword) => ({
                          label: keyword,
                          value: keyword,
                        }))}
                        onChange={(newValue) =>
                          field.onChange(newValue.map((option) => option.value))
                        }
                        placeholder="Add keywords to search..."
                        creatable
                        emptyIndicator={
                          <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                            Type to create a new one.
                          </p>
                        }
                        autoLowercase
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Enter keywords to search for KOLs.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="search_video_count"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Search Video Count</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        value={field.value ?? ''}
                        max={200}
                        onChange={(e) => {
                          const value =
                            e.target.value === ''
                              ? null
                              : Number(e.target.value);
                          field.onChange(value);
                          form.trigger('search_video_count');
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                    <FormDescription>
                      Set the number of videos to search for.
                    </FormDescription>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="search_sort_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Search Sort Type {field.value!}</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value!}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a sort type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="0">Relatedness (0)</SelectItem>
                        <SelectItem value="1">Most likes (1)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    <FormDescription>
                      Select the sort type for the search results.
                    </FormDescription>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="search_publish_time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Search Publish Time {field.value}</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value!}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a publish time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="0">Unlimited (0)</SelectItem>
                        <SelectItem value="1">Last 24 hours (1)</SelectItem>
                        <SelectItem value="7">Last week (7)</SelectItem>
                        <SelectItem value="30">Last month (30)</SelectItem>
                        <SelectItem value="90">
                          Last three months (90)
                        </SelectItem>
                        <SelectItem value="180">
                          Last half year (180)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    <FormDescription>
                      Set the time range for the search.
                    </FormDescription>
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6 grid grid-cols-1 gap-6">
              <Card className="transition-all duration-300 rounded-sm">
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    <span>Basic Filter Conditions</span>
                    <FormField
                      control={form.control}
                      name="basic_conditions_enabled"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardTitle>
                  <CardDescription>
                    Set basic conditions for KOL scouting
                  </CardDescription>
                </CardHeader>
                <CardContent
                  className={`transition-all duration-200 ${form.watch('basic_conditions_enabled') ? '' : 'hidden'}`}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <SwitchableCondition
                      form={form}
                      name="basic_conditions.target_region"
                      label="Target Region"
                      description="Set the target region for KOL scouting"
                      inputType="text"
                      defaultValue="jp"
                    />
                    {/* <SwitchableCondition
                    form={form}
                    name="basic_conditions.target_language"
                    label="Target Language"
                    description="Set the target language for KOL scouting"
                    inputType="text"
                    defaultValue="ja"
                  /> */}
                    <SwitchableCondition
                      form={form}
                      name="basic_conditions.min_followers"
                      label="Minimum Followers"
                      description="Set the minimum number of followers"
                      inputType="number"
                      min={0}
                      defaultValue={10000}
                    />
                    <SwitchableCondition
                      form={form}
                      name="basic_conditions.min_like_view_ratio"
                      label="Minimum Like/View Ratio"
                      description="Set the minimum like/view ratio"
                      inputType="number"
                      min={0}
                      max={1}
                      step={0.001}
                      defaultValue={0.05}
                    />
                    <SwitchableCondition
                      form={form}
                      name="basic_conditions.min_comment_like_ratio"
                      label="Minimum Comment/Like Ratio"
                      description="Set the minimum comment/like ratio"
                      inputType="number"
                      min={0}
                      max={1}
                      step={0.001}
                      defaultValue={0.002}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="transition-all duration-300 rounded-sm">
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    <span>Video Filter Conditions</span>
                    <FormField
                      control={form.control}
                      name="video_conditions_enabled"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardTitle>
                  <CardDescription>
                    Set video-specific conditions for KOL scouting
                  </CardDescription>
                </CardHeader>
                <CardContent
                  className={`transition-all duration-200 ${form.watch('video_conditions_enabled') ? '' : 'hidden'}`}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <SwitchableCondition
                      form={form}
                      name="video_conditions.months_to_check"
                      label="Months to Check"
                      description="Set the number of months to check"
                      inputType="number"
                      min={1}
                      max={12}
                      defaultValue={6}
                    />
                    <SwitchableCondition
                      form={form}
                      name="video_conditions.videos_to_check"
                      label="Videos to Check"
                      description="Set the number of videos to check"
                      inputType="number"
                      min={1}
                      max={25}
                      defaultValue={20}
                    />
                    <SwitchableCondition
                      form={form}
                      name="video_conditions.min_videos_in_period"
                      label="Minimum Videos in Period"
                      description="Set the minimum number of videos in the period"
                      inputType="number"
                      min={1}
                      defaultValue={9}
                    />
                    <SwitchableCondition
                      form={form}
                      name="video_conditions.min_published_days"
                      label="Minimum Published Days"
                      description="Set the minimum number of days since publication"
                      inputType="number"
                      min={1}
                      defaultValue={7}
                    />
                    <SwitchableCondition
                      form={form}
                      name="video_conditions.min_avg_views"
                      label="Minimum Average Views"
                      description="Set the minimum average views"
                      inputType="number"
                      min={0}
                      defaultValue={10000}
                    />
                    <SwitchableCondition
                      form={form}
                      name="video_conditions.min_avg_likes"
                      label="Minimum Average Likes"
                      description="Set the minimum average likes"
                      inputType="number"
                      min={0}
                      defaultValue={1000}
                    />
                    {/* <SwitchableCondition
                    form={form}
                    name="video_conditions.min_avg_comments"
                    label="Minimum Average Comments"
                    description="Set the minimum average comments"
                    inputType="number"
                    min={0}
                    defaultValue={100}
                  /> */}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex gap-2 mt-6">
              <Button
                type="submit"
                className="w-full md:w-auto"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-xs mr-2"></span>
                    Submitting...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (!isSubmitting) {
                    form.reset(initialValues);
                    saveFormState(initialValues);
                    toast.info('Scout creation config reset!');
                  }
                }}
                className="w-full md:w-auto"
                disabled={isSubmitting}
              >
                Reset
              </Button>
            </div>
          </fieldset>
        </form>
      </Form>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Scout Creation</DialogTitle>
            <DialogDescription>
              Are you sure you want to create a new scout with these settings?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                setIsDialogOpen(false);
                confirmSubmit();
              }}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
