'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import useS<PERSON> from 'swr';
import {
  Loader2,
  X,
  Columns3,
  FileDown,
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  MoreHorizontal,
  ChevronRightIcon,
  ChevronsRight,
  ChevronLeftIcon,
  ChevronsLeft,
} from 'lucide-react';
import FilterOptions from '@/components/FilterOptions';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { toast } from 'sonner';
import { exportToXLS } from '@/lib/utils';
import { useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Tables } from '@/lib/database.types';

export type Video = Tables<'kol_scout_videos'>;

export default function KOLScoutVideos() {
  const searchParams = useSearchParams();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [filters, setFilters] = useState<Record<string, string>>({
    description: '',
    region: '',
    scoutId: searchParams?.get('scout_id') || '',
    minViews: '',
    minLikes: '',
  });

  const filterFields = [
    {
      id: 'description',
      label: 'Description',
      placeholder: 'Filter descriptions...',
    },
    { id: 'region', label: 'Region', placeholder: 'Filter region...' },
    { id: 'scoutId', label: 'Scout ID', placeholder: 'Filter Scout ID...' },
    {
      id: 'minViews',
      label: 'Min Views',
      placeholder: 'Min views...',
      type: 'number',
    },
    {
      id: 'minLikes',
      label: 'Min Likes',
      placeholder: 'Min likes...',
      type: 'number',
    },
  ];

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    id: false,
    description: true,
    view_count: true,
    like_count: true,
    region: true,
    create_time: false,
    comment_count: false,
    share_count: false,
    language: false,
  });
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  function applyFiltersQuery(query: any) {
    if (filters.description && filters.description !== '') {
      query = query.ilike('description', `%${filters.description}%`);
    }

    if (filters.region && filters.region !== '') {
      query = query.ilike('region', filters.region);
    }

    if (filters.scoutId && filters.scoutId !== '') {
      query = query.eq('scout_id', filters.scoutId);
    }

    if (filters.minViews && filters.minViews !== '') {
      query = query.gte('view_count', parseInt(filters.minViews));
    }

    if (filters.minLikes && filters.minLikes !== '') {
      query = query.gte('like_count', parseInt(filters.minLikes));
    }

    if (sorting.length > 0) {
      const { id, desc } = sorting[0];
      query = query.order(id, { ascending: !desc });
    }

    return query;
  }

  const fetchVideos = useCallback(async () => {
    const supabase = createClient();
    let query = supabase
      .from('kol_scout_videos')
      .select('*', { count: 'exact' })
      .range(
        pagination.pageIndex * pagination.pageSize,
        (pagination.pageIndex + 1) * pagination.pageSize - 1
      );

    query = applyFiltersQuery(query);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return { videos: data || [], totalCount: count || 0 };
  }, [pagination.pageIndex, pagination.pageSize, filters, sorting]);

  const { data, error, isLoading, mutate } = useSWR(
    ['videos', pagination, filters, sorting],
    fetchVideos
  );

  const videos = data?.videos || [];
  const totalCount = data?.totalCount || 0;
  const pageCount = Math.ceil(totalCount / pagination.pageSize);

  const applyFilters = useCallback(
    (newFilters: Record<string, string>) => {
      // console.log('Applying filters:', newFilters)
      setFilters(newFilters);
      localStorage.setItem(
        'videoFilters:/dashboard/scout/videos',
        JSON.stringify(newFilters)
      );
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      mutate();
    },
    [mutate]
  );

  useEffect(() => {
    try {
      const savedFilters = localStorage.getItem(
        'videoFilters:/dashboard/scout/videos'
      );
      if (savedFilters) {
        const parsedFilters = JSON.parse(savedFilters);
        setFilters(parsedFilters);
        applyFilters(parsedFilters);
      }
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }

    const scout_id = searchParams?.get('scout_id');
    if (scout_id) {
      setFilters((prev) => ({ ...prev, scoutId: scout_id }));
    }
  }, [searchParams, applyFilters]);

  const fetchAllVideos = useCallback(async () => {
    const supabase = createClient();
    let query = supabase.from('kol_scout_videos').select('*');

    query = applyFiltersQuery(query);

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return data || [];
  }, [filters, sorting]);

  const createSortableColumn = useCallback(
    (
      accessorKey: keyof Video,
      header: string,
      alignment: 'start' | 'end' = 'start'
    ): ColumnDef<Video> => ({
      accessorKey,
      header: ({ column }) => {
        const isSorted = column.getIsSorted();
        return (
          <Button
            variant={isSorted ? 'secondary' : 'ghost'}
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className={`w-full justify-${alignment} px-4 py-2`}
          >
            <span className="mr-2">{header}</span>
            {isSorted === 'asc' && <ChevronUp className="h-4 w-4" />}
            {isSorted === 'desc' && <ChevronDown className="h-4 w-4" />}
            {!isSorted && <ChevronsUpDown className="h-4 w-4" />}
          </Button>
        );
      },
      cell: ({ row }) => {
        const value = row.getValue(accessorKey);
        const cellContent = (() => {
          if (accessorKey === 'create_time') {
            return new Date((value as number) * 1000).toLocaleString();
          }
          if (accessorKey === 'description') {
            return (
              <div className="max-w-[350px] truncate" title={value as string}>
                {value as string}
              </div>
            );
          }
          return value as string | number;
        })();
        return (
          <div className={`text-${alignment} px-4 py-2`}>{cellContent}</div>
        );
      },
    }),
    []
  );

  const columns = useMemo<ColumnDef<Video>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      createSortableColumn('id', 'ID'),
      createSortableColumn('description', 'Description'),
      createSortableColumn('create_time', 'Create Time'),
      createSortableColumn('view_count', 'Views'),
      createSortableColumn('like_count', 'Likes'),
      createSortableColumn('comment_count', 'Comments'),
      createSortableColumn('share_count', 'Shares'),
      createSortableColumn('region', 'Region'),
      createSortableColumn('language', 'Language'),
      {
        id: 'actions',
        enableHiding: false,
        cell: ({ row }) => {
          const video = row.original;

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    navigator.clipboard.writeText(video.id);
                    toast.success(`Video ID ${video.id} copied to clipboard`);
                  }}
                >
                  Copy video ID
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    window.open(`${video.link}`, '_blank');
                    toast.success('Video link opened in new tab');
                  }}
                >
                  Open video link
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [createSortableColumn]
  );

  const table = useReactTable({
    data: videos,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    manualPagination: true,
    manualSorting: true,
    pageCount,
  });

  const handleFilterChange = useCallback(
    (key: string, value: string) => {
      setFilters((prev) => {
        const newFilters = { ...prev, [key]: value };
        localStorage.setItem(
          'videoFilters:/dashboard/scout/videos',
          JSON.stringify(newFilters)
        );
        return newFilters;
      });
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      mutate();
    },
    [mutate]
  );

  return (
    <>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/scout">KOL Scout</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Scouted Videos</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold">Scouted Videos</h3>
        <div className="flex space-x-2">
          <FilterOptions
            filters={filters}
            setFilters={setFilters}
            applyFilters={applyFilters}
            filterFields={filterFields}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Columns3 className="mr-2 h-4 w-4" />
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="text-red-500">
                <FileDown className="mr-2 h-4 w-4" />
                Export <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  const selectedRows = table.getFilteredSelectedRowModel().rows;
                  if (selectedRows.length === 0) {
                    toast.error('No rows selected for export');
                    return;
                  }
                  const fileName = 'ScoutedVideos';
                  const allVideos = selectedRows.map((row) => row.original);
                  exportToXLS(allVideos, fileName);
                  toast.success(
                    `Exported ${selectedRows.length} rows to Excel`
                  );
                }}
              >
                Export SELECTED (
                {table.getFilteredSelectedRowModel().rows.length}) rows
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={async () => {
                  try {
                    toast.info('Exporting all videos...');
                    const fileName = 'ScoutedVideos';
                    const allVideos = await fetchAllVideos();
                    exportToXLS(allVideos, fileName);
                    toast.success(
                      `Exported all ${allVideos.length} rows to Excel`
                    );
                  } catch (error) {
                    console.error('Error fetching all videos:', error);
                    toast.error(
                      'Failed to export all videos. Please try again.'
                    );
                  }
                }}
              >
                Export ALL ({totalCount}) rows
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {Object.entries(filters).some(([_, value]) => value !== '') && (
        <>
          <Separator className="mb-2" />
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(
              ([key, value]) =>
                value && (
                  <Badge key={key} variant="secondary" className="text-sm">
                    {key}: {value}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 ml-2"
                      onClick={() => handleFilterChange(key, '')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )
            )}
          </div>
          <div className="m-2"></div>
        </>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="w-40 text-sm text-muted-foreground hidden lg:block">
          {table.getFilteredSelectedRowModel().rows.length} of {totalCount}{' '}
          selected
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.firstPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <p className="text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of {pageCount}
          </p>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.lastPage()}
            disabled={!table.getCanNextPage()}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Page size</p>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              const newPageSize = Number(e.target.value);
              table.setPageSize(newPageSize);
            }}
            className="h-8 w-20 rounded-md border border-input bg-background px-2"
          >
            {[10, 50, 200].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
        </div>
      </div>
    </>
  );
}
