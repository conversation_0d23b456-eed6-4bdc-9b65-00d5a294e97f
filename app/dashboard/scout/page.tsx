'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { toast } from 'sonner';
import ScoutStatusBadge from '@/components/ScoutStatusBadge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Loader2,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Eye,
  Copy,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { createClient } from '@/lib/supabase/client';

const supabase = createClient();

export default function Scouts() {
  return (
    <React.StrictMode>
      <ScoutsContent />
    </React.StrictMode>
  );
}

interface Scout {
  id: string;
  updated_time: string;
  search_keywords: string[];
  search_video_count: number;
  filter_conditions: any;
  status: string;
}

function ScoutsContent() {
  const [scouts, setScouts] = useState<Scout[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const fetchScouts = useCallback(async (page: number) => {
    setIsLoading(true);
    const {
      data: { user },
    } = await supabase.auth.getUser();
    const { data, error, count } = await supabase
      .from('kol_scout_transactions')
      // .select('*')
      .select('*', { count: 'exact' })
      .eq('job_creator', user!.id)
      .range((page - 1) * 10, page * 10 - 1)
      .order('updated_time', { ascending: false });

    if (!error && data) {
      setScouts(data as Scout[]);
      setTotalPages(Math.ceil((count ?? 0) / 10));
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    fetchScouts(currentPage);
    const intervalId = setInterval(() => fetchScouts(currentPage), 30000);
    return () => clearInterval(intervalId);
  }, [currentPage, fetchScouts]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedConditions, setSelectedConditions] = useState<any>(null);

  const showConditionsDialog = (conditions: any) => {
    setSelectedConditions(conditions);
    setIsDialogOpen(true);
  };

  const compactJSON = (input: object | string): JSX.Element => {
    try {
      const obj = typeof input === 'string' ? JSON.parse(input) : input;
      const { basic_conditions, video_conditions } = obj;

      const formatSection = (section: object, title: string) =>
        section ? (
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">{title}</h3>
            {Object.entries(section).map(([key, value]) => (
              <div key={key} className="text-sm mb-1">
                <span className="font-medium">{key}:</span> {value}
              </div>
            ))}
          </div>
        ) : (
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">{title}</h3>
            <div className="text-sm text-muted-foreground">Not set</div>
          </div>
        );

      return (
        <div className="text-left">
          {formatSection(basic_conditions, 'Basic Conditions')}
          {formatSection(video_conditions, 'Video Conditions')}
        </div>
      );
    } catch (error) {
      console.error('Error processing input:', error);
      return <div className="text-red-500">Invalid input</div>;
    }
  };

  return (
    <>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>KOL Scout</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-3xl font-bold">All Scouts</h2>
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => fetchScouts(currentPage)}
            variant="outline"
            size="icon"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button onClick={() => router.push('/dashboard/scout/create')}>
            <Plus className="w-4 h-4 mr-1" /> Create New Scout
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Created At</TableHead>
              <TableHead>Keywords</TableHead>
              <TableHead>Search Videos</TableHead>
              <TableHead>Conditions</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex items-center justify-center">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </div>
                </TableCell>
              </TableRow>
            ) : scouts.length > 0 ? (
              scouts.map((scout) => (
                <TableRow key={scout.id}>
                  <TableCell>
                    {new Date(scout.updated_time).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {scout.search_keywords &&
                        scout.search_keywords.map((keyword, index) => (
                          <Badge key={index} variant="secondary">
                            {keyword}
                          </Badge>
                        ))}
                    </div>
                  </TableCell>
                  <TableCell>{scout.search_video_count}</TableCell>
                  <TableCell>
                    <Button
                      onClick={() =>
                        showConditionsDialog(scout.filter_conditions)
                      }
                      variant="outline"
                      size="sm"
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      View
                    </Button>
                  </TableCell>
                  <TableCell>
                    <ScoutStatusBadge status={scout.status} />
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          aria-label="Actions"
                          size="icon"
                          variant="ghost"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Link
                            href={`/dashboard/scout/videos?scout_id=${scout.id}`}
                          >
                            Related Videos
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Link
                            href={`/dashboard/scout/creators?scout_id=${scout.id}`}
                          >
                            Related Creators
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onSelect={(e) => {
                            e.preventDefault();
                            navigator.clipboard.writeText(scout.id);
                            toast.success('Scout ID copied to clipboard');
                          }}
                        >
                          Copy ID
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No scouts found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between space-x-2 py-4">
        <p className="text-sm text-muted-foreground">
          Showing {scouts.length} of {totalPages * 10} scouts
        </p>
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">
            Page {currentPage} of {totalPages}
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Scout Conditions</DialogTitle>
            <DialogDescription>
              Detailed view of the filter conditions.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 max-h-[60vh] overflow-y-auto">
            {selectedConditions && compactJSON(selectedConditions)}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
