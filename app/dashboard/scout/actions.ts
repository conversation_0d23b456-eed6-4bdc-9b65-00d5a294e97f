'use server';

import { createServerClient } from '@/lib/supabase/server';

export async function getScouts(page: number) {
  const supabase = createServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const { data, error, count } = await supabase
    .from('kol_scout_transactions')
    .select('*', { count: 'exact' })
    .eq('job_creator', user!.id)
    .range((page - 1) * 10, page * 10 - 1)
    .order('updated_time', { ascending: false });

  if (error) {
    console.error('Error fetching scouts:', error);
    return { scouts: [], totalPages: 0 };
  }

  const totalPages = Math.ceil((count ?? 0) / 10);

  return {
    scouts: data,
    totalPages,
  };
}
