import { ColumnDef } from "@tanstack/react-table"
import { Tables } from '@/lib/database.types'

export const columns: ColumnDef<Tables<'kol_scout_videos'>>[] = [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "view_count",
    header: "Views",
  },
  {
    accessorKey: "like_count",
    header: "Likes",
  },
  {
    accessorKey: "comment_count",
    header: "Comments",
  },
  {
    accessorKey: "create_time",
    header: "Created At",
    cell: ({ row }) => {
      const timestamp = row.getValue("create_time") as number | null;
      if (timestamp) {
        return new Date(timestamp * 1000).toLocaleString();
      }
      return "N/A";
    },
  },
  {
    accessorKey: "link",
    header: "Link",
    cell: ({ row }) => {
      const link = row.getValue("link") as string | null;
      return link ? <a href={link} target="_blank" rel="noopener noreferrer">View</a> : "N/A";
    },
  },
]
