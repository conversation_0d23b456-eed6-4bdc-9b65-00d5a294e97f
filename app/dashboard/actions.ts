'use client';

import { createClient } from '@/lib/supabase/client';

const supabase = createClient();

export interface Metric {
  title: string;
  value: number;
  incrementToday?: number;
  incrementThisWeek?: number;
}

export interface Scout {
  id: string;
  status: string;
  search_keywords: string[];
  search_video_count: number;
  filter_conditions: string;
  updated_time: string;
  job_creator: string;
}

export interface MetricView {
  total: number;
  today: number;
  this_week: number;
}

export type MetricKey =
  | 'totalVideos'
  | 'totalCreators'
  | 'totalScouts'
  | 'totalCreatorMetrics';

export async function getTotalVideos(): Promise<Metric> {
  return getMetricData('kol_scout_video_summary', 'Total Videos');
}

export function getTotalCreators(): Promise<Metric> {
  return getMetricData('kol_scout_creator_summary', 'Total Creators');
}

export function getTotalScouts(): Promise<Metric> {
  return getMetricData('kol_scout_transaction_summary', 'Total Scouts');
}

export function getTotalCreatorMetrics(): Promise<Metric> {
  return getMetricData('kol_scout_creator_metrics', 'Total Creator Metrics');
}

async function getMetricData(
  table:
    | 'kol_scout_video_summary'
    | 'kol_scout_creator_summary'
    | 'kol_scout_transaction_summary'
    | 'kol_scout_creator_metrics',
  title: string,
  includeIncrement: boolean = true
): Promise<Metric> {
  if (table === 'kol_scout_creator_metrics') {
    const { count, error } = await supabase
      .from(table)
      .select('*', { count: 'exact' });
    if (error) throw error;
    return { title, value: count ?? 0 };
  }

  const { data, error } = await supabase.from(table).select('*');

  if (error) throw error;

  if (data && data.length > 0) {
    const { total, today, this_week } = data[0];

    return {
      title,
      value: total ?? 0,
      incrementToday: today ?? 0,
      incrementThisWeek: this_week ?? 0,
    };
  }

  return { title, value: 0 };
}

export async function getRecentScouts(): Promise<Scout[]> {
  // const { data: {user} } = await supabase.auth.getUser();
  const { data, error } = await supabase
    .from('kol_scout_transactions')
    .select(
      'id, status, search_keywords, search_video_count, filter_conditions, updated_time, job_creator'
    )
    .order('updated_time', { ascending: false })
    .limit(5);
  if (error) throw error;
  return data as Scout[];
}
