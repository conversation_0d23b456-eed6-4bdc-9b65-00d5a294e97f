'use client'

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import ScoutStatusBadge from '@/components/ScoutStatusBadge';
import { Skeleton } from "@/components/ui/skeleton";
import { getRecentScouts, Scout } from './actions';
import { createClient } from '@/lib/supabase/client';

function ScoutTableSkeleton() {
    return (
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Created At</TableHead>
                    <TableHead>Keywords</TableHead>
                    <TableHead>Search Videos</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[50px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                        <TableCell className="text-right"><Skeleton className="h-8 w-8 rounded-full ml-auto" /></TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    );
}

export default function ActivitiesSection() {
    const [recentScouts, setRecentScouts] = useState<Scout[] | null>(null);

    useEffect(() => {
        getRecentScouts().then(setRecentScouts);
    }, []);

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle>Recent Scouts</CardTitle>
                <Button variant="link" asChild>
                    <Link href="/dashboard/scout">SHOW ALL</Link>
                </Button>
            </CardHeader>
            <CardContent>
                {recentScouts === null ? (
                    <ScoutTableSkeleton />
                ) : recentScouts.length === 0 ? (
                    <p className="text-center text-muted-foreground py-4">No recent scouts found.</p>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Created At</TableHead>
                                <TableHead>Keywords</TableHead>
                                <TableHead>Search Videos</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {recentScouts.map((scout) => (
                                <TableRow key={scout.id}>
                                    <TableCell>{new Date(scout.updated_time).toLocaleString()}</TableCell>
                                    <TableCell>
                                        <div className="flex flex-wrap gap-1">
                                            {scout.search_keywords && scout.search_keywords.map((keyword, index) => (
                                                <Badge key={index} variant="secondary">{keyword}</Badge>
                                            ))}
                                        </div>
                                    </TableCell>
                                    <TableCell>{scout.search_video_count}</TableCell>
                                    <TableCell>
                                        <ScoutStatusBadge status={scout.status} />
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button aria-label="Actions" size="icon" variant="ghost"><MoreHorizontal className="h-4 w-4" /></Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                <DropdownMenuItem>
                                                    <Link href={`/dashboard/scout/videos?scout_id=${scout.id}`}>Related Videos</Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Link href={`/dashboard/scout/creators?scout_id=${scout.id}`}>Related Creators</Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Link href={`/dashboard/scout/copy/${scout.id}`}>Copy ID</Link>
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </CardContent>
        </Card>
    );
}
