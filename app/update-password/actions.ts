'use server'

import { createServerClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

export async function updatePassword(formData: FormData) {
  const supabase = createServerClient()

  // Check if the "Go Back" button was clicked
  if (formData.get('formAction') === '/dashboard') {
    redirect('/dashboard')
  }

  const password = formData.get('password') as string
  const confirmPassword = formData.get('confirmPassword') as string

  if (password !== confirmPassword) {
    redirect('/update-password?error=Passwords do not match')
  }

  const { error } = await supabase.auth.updateUser({ 
    password,
    data: { has_set_password: true }
  })

  if (error) {
    redirect(`/update-password?error=${error.message}`)
  }

  revalidatePath('/', 'layout')
  redirect('/dashboard')
}
