import { redirect } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { updatePassword } from "./actions";
import { createServerClient } from "@/lib/supabase/server";
import Link from "next/link";

export default async function UpdatePasswordPage(
  { params,
    searchParams
  }: {
    params: { slug: string }
    searchParams: { error?: string }
  }
) {
  const supabase = createServerClient()
  const { data: { session } } = await supabase.auth.getSession();
  const { error } = searchParams;

  if (!session) {
    redirect("/login");
  }

  const { data: { user } } = await supabase.auth.getUser();
  const hasSetPasswordBefore = user?.user_metadata?.has_set_password;

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4">
      <Card className="mx-auto w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Update Password</CardTitle>
          <CardDescription>
            Enter your new password below
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <form className="space-y-4" action={updatePassword}>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={user?.email}
                disabled
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">New Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
              />
            </div>
            {error && <p className="text-red-500 text-sm">{error}</p>}
            <Button className="w-full" type="submit">
              Update
            </Button>
          </form>
          {hasSetPasswordBefore && (
            <Button variant="outline" className="w-full" >
              <Link href="/dashboard">
                Go Back
              </Link>
            </Button>
          )}
        </CardContent>
      </Card>
    </main>
  );
}
