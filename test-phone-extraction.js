// Test script for phone number extraction
const extractPhoneFromSignature = (signature) => {
  // Phone number patterns - covers various formats including Unicode characters
  const phonePatterns = [
    // International format with various separators (including Unicode en dash ‑, em dash —, minus −)
    // Matches: +62 851‑2105‑6618, +44-20-7946-0958, +86 138 0013 8000
    /\+[1-9]\d{1,3}[\s\-‑—−.]*\d{1,4}[\s\-‑—−.]*\d{1,4}[\s\-‑—−.]*\d{1,9}[\s\-‑—−.]*\d{0,4}/g,
    
    // US/International formats with parentheses and various separators
    // Matches: ******-567-8900, +1 (234) 567‑8900, ******.567.8900
    /\+?1?[\s\-‑—−.]?\(?([0-9]{3})\)?[\s\-‑—−.]?([0-9]{3})[\s\-‑—−.]?([0-9]{4})/g,
    
    // Simple formats with various separators
    // Matches: ************, 123‑456‑7890, ************, ************
    /\b\d{3}[\s\-‑—−.]?\d{3}[\s\-‑—−.]?\d{4}\b/g,
    
    // Parentheses format with various separators
    // Matches: (*************, (123)‑456‑7890
    /\(\d{3}\)[\s\-‑—−.]?\d{3}[\s\-‑—−.]?\d{4}/g,
    
    // More flexible international pattern for longer numbers
    // Matches various international formats with 7-15 digits total
    /\+\d{1,3}[\s\-‑—−.]*(?:\d[\s\-‑—−.]*){6,14}\d/g,
  ];

  for (const pattern of phonePatterns) {
    const matches = signature.match(pattern);
    if (matches) {
      // Find the longest match (most complete phone number)
      const longestMatch = matches.reduce((longest, current) => 
        current.length > longest.length ? current : longest
      );
      return longestMatch.trim();
    }
  }
  
  return '';
};

// Test cases
const testCases = [
  'Endorse : Gladys ( +62 851‑2105‑6618 )',
  'Contact me at ******-567-8900',
  'Call +44 20 7946 0958',
  'Phone: +86 138 0013 8000',
  'My number is (*************',
  'Text me: 123‑456‑7890',
  'WhatsApp: +62 812—3456—7890',
  'No phone number here',
];

console.log('Testing phone number extraction:');
console.log('================================');

testCases.forEach((testCase, index) => {
  const result = extractPhoneFromSignature(testCase);
  console.log(`${index + 1}. "${testCase}"`);
  console.log(`   Extracted: "${result}"`);
  console.log('');
});
